import 'package:flutter/material.dart';
import '../models/payment_status.dart';
import '../services/payment_service.dart';

class PaymentStatusScreen extends StatefulWidget {
  const PaymentStatusScreen({super.key});

  @override
  State<PaymentStatusScreen> createState() => _PaymentStatusScreenState();
}

class _PaymentStatusScreenState extends State<PaymentStatusScreen> with SingleTickerProviderStateMixin {
  final PaymentService _paymentService = PaymentService();
  late TabController _tabController;
  
  Map<String, dynamic> _statistics = {};
  bool _isLoadingStats = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadStatistics();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadStatistics() async {
    try {
      final stats = await _paymentService.getPaymentStatistics();
      if (mounted) {
        setState(() {
          _statistics = stats;
          _isLoadingStats = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingStats = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading statistics: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showCashHandoverDialog(PaymentRecord payment) {
    final pinController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Cash Handover'),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Order #${payment.orderNumber}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text('Amount: ${payment.formattedAmount}'),
              Text('Delivery Person: ${payment.deliveryPersonName ?? 'Unknown'}'),
              const SizedBox(height: 16),
              TextFormField(
                controller: pinController,
                decoration: const InputDecoration(
                  labelText: 'Admin PIN',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.lock),
                ),
                obscureText: true,
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter PIN';
                  }
                  if (value.length < 4) {
                    return 'PIN must be at least 4 digits';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.amber.shade50,
                  border: Border.all(color: Colors.amber.shade200),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning, color: Colors.amber.shade700),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Confirm that you have received the cash from the delivery person.',
                        style: TextStyle(
                          color: Colors.amber.shade700,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (formKey.currentState!.validate()) {
                try {
                  await _paymentService.confirmCashHandover(
                    payment.orderId,
                    pinController.text,
                  );
                  
                  if (mounted) {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Cash handover confirmed successfully'),
                        backgroundColor: Colors.green,
                      ),
                    );
                    _loadStatistics(); // Refresh statistics
                  }
                } catch (e) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Error: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
            child: const Text('Confirm Handover'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment Status'),
        backgroundColor: Colors.green.shade600,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'Pending Handover'),
            Tab(text: 'All Payments'),
            Tab(text: 'Statistics'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildPendingHandoverTab(),
          _buildAllPaymentsTab(),
          _buildStatisticsTab(),
        ],
      ),
    );
  }

  Widget _buildPendingHandoverTab() {
    return StreamBuilder<List<PaymentRecord>>(
      stream: _paymentService.getCashPaymentsNeedingHandover(),
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return Center(child: Text('Error: ${snapshot.error}'));
        }

        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.check_circle_outline,
                  size: 64,
                  color: Colors.green,
                ),
                SizedBox(height: 16),
                Text(
                  'No pending cash handovers',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'All cash payments have been handed over',
                  style: TextStyle(color: Colors.grey),
                ),
              ],
            ),
          );
        }

        final payments = snapshot.data!;

        return RefreshIndicator(
          onRefresh: _loadStatistics,
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: payments.length,
            itemBuilder: (context, index) {
              final payment = payments[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 12),
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundColor: Colors.orange.shade600,
                    child: const Icon(
                      Icons.money,
                      color: Colors.white,
                    ),
                  ),
                  title: Text('Order #${payment.orderNumber}'),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Customer: ${payment.customerName}'),
                      Text('Delivery: ${payment.deliveryPersonName ?? 'Unknown'}'),
                      Text('Collected: ${payment.collectedAt?.toString().split('.')[0] ?? 'Unknown'}'),
                    ],
                  ),
                  trailing: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        payment.formattedAmount,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      const SizedBox(height: 4),
                      ElevatedButton(
                        onPressed: () => _showCashHandoverDialog(payment),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          minimumSize: const Size(80, 30),
                        ),
                        child: const Text('Receive'),
                      ),
                    ],
                  ),
                  isThreeLine: true,
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildAllPaymentsTab() {
    return StreamBuilder<List<PaymentRecord>>(
      stream: _paymentService.getPaymentRecords(),
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return Center(child: Text('Error: ${snapshot.error}'));
        }

        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.payment,
                  size: 64,
                  color: Colors.grey,
                ),
                SizedBox(height: 16),
                Text(
                  'No payment records found',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          );
        }

        final payments = snapshot.data!;

        return RefreshIndicator(
          onRefresh: _loadStatistics,
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: payments.length,
            itemBuilder: (context, index) {
              final payment = payments[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 12),
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundColor: payment.isUpiPayment
                        ? Colors.blue.shade600
                        : payment.status == PaymentCollectionStatus.handedToAdmin
                            ? Colors.green.shade600
                            : Colors.orange.shade600,
                    child: Icon(
                      payment.isUpiPayment ? Icons.qr_code : Icons.money,
                      color: Colors.white,
                    ),
                  ),
                  title: Text('Order #${payment.orderNumber}'),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Customer: ${payment.customerName}'),
                      if (payment.deliveryPersonName != null)
                        Text('Delivery: ${payment.deliveryPersonName}'),
                      Text('Method: ${payment.paymentMethod.toUpperCase()}'),
                      Text('Status: ${payment.statusDisplayName}'),
                    ],
                  ),
                  trailing: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        payment.formattedAmount,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: _getStatusColor(payment.status).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: _getStatusColor(payment.status)),
                        ),
                        child: Text(
                          _getStatusShortText(payment.status),
                          style: TextStyle(
                            color: _getStatusColor(payment.status),
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  isThreeLine: true,
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildStatisticsTab() {
    if (_isLoadingStats) {
      return const Center(child: CircularProgressIndicator());
    }

    return RefreshIndicator(
      onRefresh: _loadStatistics,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Payment Overview',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Cash Payment Statistics
            Text(
              'Cash Payments',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.green.shade700,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Pending Handover',
                    '₹${(_statistics['pendingCashHandover'] ?? 0.0).toStringAsFixed(2)}',
                    '${_statistics['pendingCashCount'] ?? 0} orders',
                    Icons.pending_actions,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Today\'s Cash',
                    '₹${(_statistics['todayCashCollected'] ?? 0.0).toStringAsFixed(2)}',
                    'Collected by delivery',
                    Icons.money,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // UPI Payment Statistics
            Text(
              'UPI Payments',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.blue.shade700,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Today\'s UPI',
                    '₹${(_statistics['todayUpiReceived'] ?? 0.0).toStringAsFixed(2)}',
                    'Received directly',
                    Icons.qr_code,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Total UPI',
                    '₹${(_statistics['totalUpiReceived'] ?? 0.0).toStringAsFixed(2)}',
                    '${_statistics['totalUpiCount'] ?? 0} transactions',
                    Icons.account_balance,
                    Colors.indigo,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Overall Statistics
            Text(
              'Overall Summary',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.purple.shade700,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Monthly Total',
                    '₹${(_statistics['monthlyTotal'] ?? 0.0).toStringAsFixed(2)}',
                    'All payments',
                    Icons.analytics,
                    Colors.purple,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Container(), // Empty space for symmetry
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Quick Actions
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Column(
                children: [
                  ListTile(
                    leading: const Icon(Icons.pending_actions, color: Colors.orange),
                    title: const Text('View Pending Cash Handovers'),
                    subtitle: Text('${_statistics['pendingCashCount'] ?? 0} cash payments waiting'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () {
                      _tabController.animateTo(0); // Switch to pending tab
                    },
                  ),
                  const Divider(height: 1),
                  ListTile(
                    leading: const Icon(Icons.qr_code, color: Colors.blue),
                    title: const Text('View UPI Payments'),
                    subtitle: Text('${_statistics['totalUpiCount'] ?? 0} UPI transactions received'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () {
                      _showUpiPaymentsDialog();
                    },
                  ),
                  const Divider(height: 1),
                  ListTile(
                    leading: const Icon(Icons.settings, color: Colors.green),
                    title: const Text('UPI Settings'),
                    subtitle: const Text('Configure UPI ID and payment details'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () {
                      Navigator.pushNamed(context, '/settings');
                    },
                  ),
                  const Divider(height: 1),
                  ListTile(
                    leading: const Icon(Icons.history, color: Colors.purple),
                    title: const Text('All Payment History'),
                    subtitle: const Text('View complete payment records'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () {
                      _tabController.animateTo(1); // Switch to all payments tab
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, String subtitle, IconData icon, Color color) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            Text(
              subtitle,
              style: const TextStyle(
                fontSize: 10,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(PaymentCollectionStatus status) {
    switch (status) {
      case PaymentCollectionStatus.pending:
        return Colors.grey;
      case PaymentCollectionStatus.collectedByDelivery:
        return Colors.orange;
      case PaymentCollectionStatus.handedToAdmin:
        return Colors.green;
      case PaymentCollectionStatus.upiReceived:
        return Colors.blue;
    }
  }

  String _getStatusShortText(PaymentCollectionStatus status) {
    switch (status) {
      case PaymentCollectionStatus.pending:
        return 'PENDING';
      case PaymentCollectionStatus.collectedByDelivery:
        return 'COLLECTED';
      case PaymentCollectionStatus.handedToAdmin:
        return 'RECEIVED';
      case PaymentCollectionStatus.upiReceived:
        return 'UPI';
    }
  }

  void _showUpiPaymentsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.qr_code, color: Colors.blue),
            SizedBox(width: 8),
            Text('UPI Payments'),
          ],
        ),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: StreamBuilder<List<PaymentRecord>>(
            stream: _paymentService.getPaymentRecords(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }

              if (snapshot.hasError) {
                return Center(child: Text('Error: ${snapshot.error}'));
              }

              if (!snapshot.hasData || snapshot.data!.isEmpty) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.qr_code_scanner, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text('No UPI payments yet'),
                    ],
                  ),
                );
              }

              // Filter only UPI payments
              final upiPayments = snapshot.data!
                  .where((payment) => payment.isUpiPayment)
                  .toList();

              if (upiPayments.isEmpty) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.qr_code_scanner, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text('No UPI payments yet'),
                    ],
                  ),
                );
              }

              return ListView.builder(
                itemCount: upiPayments.length,
                itemBuilder: (context, index) {
                  final payment = upiPayments[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      leading: const CircleAvatar(
                        backgroundColor: Colors.blue,
                        child: Icon(Icons.qr_code, color: Colors.white),
                      ),
                      title: Text(payment.formattedAmount),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('Order: ${payment.orderNumber}'),
                          Text('Date: ${payment.collectedAt?.toString().split(' ')[0] ?? 'N/A'}'),
                          Text('Status: ${payment.statusDisplayName}'),
                        ],
                      ),
                      isThreeLine: true,
                    ),
                  );
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _tabController.animateTo(1); // Switch to all payments tab
            },
            child: const Text('View All'),
          ),
        ],
      ),
    );
  }
}
