[debug] [2025-07-15T13:46:09.610Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-07-15T13:46:10.192Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-07-15T13:46:10.196Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-15T13:46:10.232Z] [Firebase Plugin] VSCode notification server listening on port 40001
[error] [Firebase Plugin] requireAuth error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.
[info] [Firebase Plugin] Stopping Data Connect toolkit
