
import 'package:flutter/material.dart';
import '../models/product_model.dart';

class CartProvider with ChangeNotifier {
  final List<Product> _products = [];

  List<Product> get products => _products;

  double get totalPrice => _products.fold(0.0, (sum, item) => sum + (item.price * item.quantity));

  void addProduct(Product product) {
    int index = _products.indexWhere((p) => p.id == product.id);
    if (index != -1) {
      _products[index].quantity++;
    } else {
      // Start with quantity 1 when first added to cart
      product.quantity = 1;
      _products.add(product);
    }
    notifyListeners();
  }

  void removeProduct(Product product) {
    int index = _products.indexWhere((p) => p.id == product.id);
    if (index != -1) {
      if (_products[index].quantity > 1) {
        _products[index].quantity--;
      } else {
        // Set quantity to 0 instead of removing from list
        _products[index].quantity = 0;
      }
    }
    notifyListeners();
  }

  void clearCart() {
    _products.clear();
    notifyListeners();
  }

  // Add product to list without changing quantity (for initial loading)
  void addProductToList(Product product) {
    int index = _products.indexWhere((p) => p.id == product.id);
    if (index == -1) {
      _products.add(product);
      notifyListeners();
    }
  }
}
