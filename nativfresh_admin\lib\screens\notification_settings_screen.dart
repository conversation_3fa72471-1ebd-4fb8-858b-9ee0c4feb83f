import 'package:flutter/material.dart';
import '../services/settings_service.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  final SettingsService _settingsService = SettingsService();
  
  Map<String, dynamic>? _notificationSettings;
  bool _isLoading = true;

  // Default notification settings
  bool _newOrderNotifications = true;
  bool _orderStatusNotifications = true;
  bool _stockAlertNotifications = true;
  bool _deliveryNotifications = true;
  bool _systemNotifications = true;
  bool _emailNotifications = false;
  bool _pushNotifications = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  String _notificationSound = 'default';
  int _quietHoursStart = 22; // 10 PM
  int _quietHoursEnd = 8;    // 8 AM
  bool _quietHoursEnabled = false;

  @override
  void initState() {
    super.initState();
    _loadNotificationSettings();
  }

  Future<void> _loadNotificationSettings() async {
    try {
      final settings = await _settingsService.getNotificationSettings();
      if (settings != null && mounted) {
        setState(() {
          _notificationSettings = settings;
          _newOrderNotifications = settings['newOrderNotifications'] ?? true;
          _orderStatusNotifications = settings['orderStatusNotifications'] ?? true;
          _stockAlertNotifications = settings['stockAlertNotifications'] ?? true;
          _deliveryNotifications = settings['deliveryNotifications'] ?? true;
          _systemNotifications = settings['systemNotifications'] ?? true;
          _emailNotifications = settings['emailNotifications'] ?? false;
          _pushNotifications = settings['pushNotifications'] ?? true;
          _soundEnabled = settings['soundEnabled'] ?? true;
          _vibrationEnabled = settings['vibrationEnabled'] ?? true;
          _notificationSound = settings['notificationSound'] ?? 'default';
          _quietHoursStart = settings['quietHoursStart'] ?? 22;
          _quietHoursEnd = settings['quietHoursEnd'] ?? 8;
          _quietHoursEnabled = settings['quietHoursEnabled'] ?? false;
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading notification settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _saveNotificationSettings() async {
    try {
      await _settingsService.updateNotificationSettings({
        'newOrderNotifications': _newOrderNotifications,
        'orderStatusNotifications': _orderStatusNotifications,
        'stockAlertNotifications': _stockAlertNotifications,
        'deliveryNotifications': _deliveryNotifications,
        'systemNotifications': _systemNotifications,
        'emailNotifications': _emailNotifications,
        'pushNotifications': _pushNotifications,
        'soundEnabled': _soundEnabled,
        'vibrationEnabled': _vibrationEnabled,
        'notificationSound': _notificationSound,
        'quietHoursStart': _quietHoursStart,
        'quietHoursEnd': _quietHoursEnd,
        'quietHoursEnabled': _quietHoursEnabled,
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Notification settings saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving notification settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showTimePickerDialog(String type) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay(
        hour: type == 'start' ? _quietHoursStart : _quietHoursEnd,
        minute: 0,
      ),
    );

    if (picked != null) {
      setState(() {
        if (type == 'start') {
          _quietHoursStart = picked.hour;
        } else {
          _quietHoursEnd = picked.hour;
        }
      });
    }
  }

  String _formatHour(int hour) {
    final time = TimeOfDay(hour: hour, minute: 0);
    return time.format(context);
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Notification Settings'),
          backgroundColor: Colors.green.shade600,
          foregroundColor: Colors.white,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Settings'),
        backgroundColor: Colors.green.shade600,
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _saveNotificationSettings,
            child: const Text(
              'Save',
              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Notification Types Section
          _buildSectionHeader('Notification Types'),
          Card(
            child: Column(
              children: [
                SwitchListTile(
                  title: const Text('New Orders'),
                  subtitle: const Text('Get notified when new orders are placed'),
                  value: _newOrderNotifications,
                  onChanged: (value) => setState(() => _newOrderNotifications = value),
                  secondary: const Icon(Icons.shopping_cart, color: Colors.blue),
                ),
                const Divider(height: 1),
                SwitchListTile(
                  title: const Text('Order Status Updates'),
                  subtitle: const Text('Get notified when order status changes'),
                  value: _orderStatusNotifications,
                  onChanged: (value) => setState(() => _orderStatusNotifications = value),
                  secondary: const Icon(Icons.update, color: Colors.orange),
                ),
                const Divider(height: 1),
                SwitchListTile(
                  title: const Text('Stock Alerts'),
                  subtitle: const Text('Get notified when stock is low'),
                  value: _stockAlertNotifications,
                  onChanged: (value) => setState(() => _stockAlertNotifications = value),
                  secondary: const Icon(Icons.inventory, color: Colors.red),
                ),
                const Divider(height: 1),
                SwitchListTile(
                  title: const Text('Delivery Updates'),
                  subtitle: const Text('Get notified about delivery status'),
                  value: _deliveryNotifications,
                  onChanged: (value) => setState(() => _deliveryNotifications = value),
                  secondary: const Icon(Icons.local_shipping, color: Colors.green),
                ),
                const Divider(height: 1),
                SwitchListTile(
                  title: const Text('System Notifications'),
                  subtitle: const Text('Get notified about system updates'),
                  value: _systemNotifications,
                  onChanged: (value) => setState(() => _systemNotifications = value),
                  secondary: const Icon(Icons.settings, color: Colors.grey),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Delivery Methods Section
          _buildSectionHeader('Delivery Methods'),
          Card(
            child: Column(
              children: [
                SwitchListTile(
                  title: const Text('Push Notifications'),
                  subtitle: const Text('Receive notifications in the app'),
                  value: _pushNotifications,
                  onChanged: (value) => setState(() => _pushNotifications = value),
                  secondary: const Icon(Icons.notifications, color: Colors.blue),
                ),
                const Divider(height: 1),
                SwitchListTile(
                  title: const Text('Email Notifications'),
                  subtitle: const Text('Receive notifications via email'),
                  value: _emailNotifications,
                  onChanged: (value) => setState(() => _emailNotifications = value),
                  secondary: const Icon(Icons.email, color: Colors.red),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Sound & Vibration Section
          _buildSectionHeader('Sound & Vibration'),
          Card(
            child: Column(
              children: [
                SwitchListTile(
                  title: const Text('Sound'),
                  subtitle: const Text('Play sound for notifications'),
                  value: _soundEnabled,
                  onChanged: (value) => setState(() => _soundEnabled = value),
                  secondary: const Icon(Icons.volume_up, color: Colors.purple),
                ),
                const Divider(height: 1),
                SwitchListTile(
                  title: const Text('Vibration'),
                  subtitle: const Text('Vibrate for notifications'),
                  value: _vibrationEnabled,
                  onChanged: (value) => setState(() => _vibrationEnabled = value),
                  secondary: const Icon(Icons.vibration, color: Colors.orange),
                ),
                const Divider(height: 1),
                ListTile(
                  title: const Text('Notification Sound'),
                  subtitle: Text(_notificationSound == 'default' ? 'Default' : _notificationSound),
                  leading: const Icon(Icons.music_note, color: Colors.green),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: _showSoundPickerDialog,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Quiet Hours Section
          _buildSectionHeader('Quiet Hours'),
          Card(
            child: Column(
              children: [
                SwitchListTile(
                  title: const Text('Enable Quiet Hours'),
                  subtitle: const Text('Disable notifications during specified hours'),
                  value: _quietHoursEnabled,
                  onChanged: (value) => setState(() => _quietHoursEnabled = value),
                  secondary: const Icon(Icons.bedtime, color: Colors.indigo),
                ),
                if (_quietHoursEnabled) ...[
                  const Divider(height: 1),
                  ListTile(
                    title: const Text('Start Time'),
                    subtitle: Text(_formatHour(_quietHoursStart)),
                    leading: const Icon(Icons.bedtime, color: Colors.indigo),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () => _showTimePickerDialog('start'),
                  ),
                  const Divider(height: 1),
                  ListTile(
                    title: const Text('End Time'),
                    subtitle: Text(_formatHour(_quietHoursEnd)),
                    leading: const Icon(Icons.wb_sunny, color: Colors.amber),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () => _showTimePickerDialog('end'),
                  ),
                ],
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Test Notification Button
          Card(
            child: ListTile(
              title: const Text('Test Notification'),
              subtitle: const Text('Send a test notification to verify settings'),
              leading: const Icon(Icons.send, color: Colors.blue),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _sendTestNotification,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.green.shade700,
        ),
      ),
    );
  }

  void _showSoundPickerDialog() {
    final sounds = ['default', 'chime', 'bell', 'notification', 'alert'];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Notification Sound'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: sounds.map((sound) => RadioListTile<String>(
            title: Text(sound == 'default' ? 'Default' : sound.toUpperCase()),
            value: sound,
            groupValue: _notificationSound,
            onChanged: (value) {
              setState(() => _notificationSound = value!);
              Navigator.pop(context);
            },
          )).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _sendTestNotification() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('🔔 Test notification sent! Check your notification panel.'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 3),
      ),
    );
  }
}
