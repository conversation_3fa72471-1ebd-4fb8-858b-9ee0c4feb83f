import 'package:cloud_firestore/cloud_firestore.dart';

enum PaymentCollectionStatus {
  pending,           // Order not yet delivered
  collectedByDelivery, // Cash collected by delivery person
  handedToAdmin,     // Cash handed over to admin
  upiReceived        // UPI payment received directly
}

class PaymentRecord {
  final String orderId;
  final String orderNumber;
  final String customerId;
  final String customerName;
  final String? deliveryPersonId;
  final String? deliveryPersonName;
  final String paymentMethod; // 'cash' or 'upi'
  final double amount;
  final PaymentCollectionStatus status;
  final DateTime? collectedAt;
  final DateTime? handedOverAt;
  final String? handedOverBy; // Admin user ID who confirmed handover
  final String? notes;

  PaymentRecord({
    required this.orderId,
    required this.orderNumber,
    required this.customerId,
    required this.customerName,
    this.deliveryPersonId,
    this.deliveryPersonName,
    required this.paymentMethod,
    required this.amount,
    required this.status,
    this.collectedAt,
    this.handedOverAt,
    this.handedOverBy,
    this.notes,
  });

  factory PaymentRecord.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return PaymentRecord(
      orderId: doc.id,
      orderNumber: data['orderNumber'] ?? '',
      customerId: data['customerId'] ?? '',
      customerName: data['customerName'] ?? '',
      deliveryPersonId: data['deliveryPersonId'],
      deliveryPersonName: data['deliveryPersonName'],
      paymentMethod: data['paymentMethod'] ?? 'cash',
      amount: (data['amount'] ?? 0).toDouble(),
      status: _parseStatus(data['status']),
      collectedAt: _parseTimestamp(data['collectedAt']),
      handedOverAt: _parseTimestamp(data['handedOverAt']),
      handedOverBy: data['handedOverBy'],
      notes: data['notes'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'orderNumber': orderNumber,
      'customerId': customerId,
      'customerName': customerName,
      'deliveryPersonId': deliveryPersonId,
      'deliveryPersonName': deliveryPersonName,
      'paymentMethod': paymentMethod,
      'amount': amount,
      'status': status.name,
      'collectedAt': collectedAt != null ? Timestamp.fromDate(collectedAt!) : null,
      'handedOverAt': handedOverAt != null ? Timestamp.fromDate(handedOverAt!) : null,
      'handedOverBy': handedOverBy,
      'notes': notes,
    };
  }

  static PaymentCollectionStatus _parseStatus(dynamic status) {
    if (status is String) {
      try {
        return PaymentCollectionStatus.values.firstWhere(
          (e) => e.name == status,
          orElse: () => PaymentCollectionStatus.pending,
        );
      } catch (e) {
        return PaymentCollectionStatus.pending;
      }
    }
    return PaymentCollectionStatus.pending;
  }

  static DateTime? _parseTimestamp(dynamic timestamp) {
    if (timestamp is Timestamp) {
      return timestamp.toDate();
    }
    if (timestamp is DateTime) {
      return timestamp;
    }
    return null;
  }

  String get statusDisplayName {
    switch (status) {
      case PaymentCollectionStatus.pending:
        return 'Pending Collection';
      case PaymentCollectionStatus.collectedByDelivery:
        return 'Collected by Delivery';
      case PaymentCollectionStatus.handedToAdmin:
        return 'Handed to Admin';
      case PaymentCollectionStatus.upiReceived:
        return 'UPI Received';
    }
  }

  String get formattedAmount => '₹${amount.toStringAsFixed(2)}';

  bool get isCashPayment => paymentMethod.toLowerCase() == 'cash';
  bool get isUpiPayment => paymentMethod.toLowerCase() == 'upi';
  bool get needsHandover => isCashPayment && status == PaymentCollectionStatus.collectedByDelivery;
}
