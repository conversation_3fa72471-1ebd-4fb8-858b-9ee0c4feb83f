import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/payment_status.dart';
import '../models/order.dart' as order_model;
import 'auth_service.dart';

class PaymentException implements Exception {
  final String message;
  PaymentException(this.message);
  
  @override
  String toString() => message;
}

class PaymentService {
  static final PaymentService _instance = PaymentService._internal();
  factory PaymentService() => _instance;
  PaymentService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AuthService _authService = AuthService();

  // Get all payment records
  Stream<List<PaymentRecord>> getPaymentRecords() {
    return _firestore
        .collection('payment_records')
        .orderBy('collectedAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => PaymentRecord.fromFirestore(doc))
            .toList());
  }

  // Get payment records by status
  Stream<List<PaymentRecord>> getPaymentRecordsByStatus(PaymentCollectionStatus status) {
    return _firestore
        .collection('payment_records')
        .where('status', isEqualTo: status.name)
        .orderBy('collectedAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => PaymentRecord.fromFirestore(doc))
            .toList());
  }

  // Get cash payments that need handover
  Stream<List<PaymentRecord>> getCashPaymentsNeedingHandover() {
    return _firestore
        .collection('payment_records')
        .where('paymentMethod', isEqualTo: 'cash')
        .where('status', isEqualTo: PaymentCollectionStatus.collectedByDelivery.name)
        .orderBy('collectedAt', descending: false)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => PaymentRecord.fromFirestore(doc))
            .toList());
  }

  // Create payment record when order is delivered
  Future<void> createPaymentRecord(order_model.Order order) async {
    try {
      if (!_authService.isAuthenticated) {
        throw PaymentException('Authentication required');
      }

      // Only create if payment was collected
      if (order.paymentCollected != true || order.paymentMethod == null) {
        return;
      }

      PaymentCollectionStatus status;
      if (order.paymentMethod == order_model.PaymentMethod.upi) {
        status = PaymentCollectionStatus.upiReceived;
      } else {
        status = PaymentCollectionStatus.collectedByDelivery;
      }

      final paymentRecord = PaymentRecord(
        orderId: order.id,
        orderNumber: order.orderNumber,
        customerId: order.customerInfo.phoneNumber, // Using phone as customer ID
        customerName: order.customerInfo.name,
        deliveryPersonId: order.assignedDeliveryPersonId,
        deliveryPersonName: order.assignedDeliveryPersonName,
        paymentMethod: order.paymentMethod!.name,
        amount: order.totalAmount,
        status: status,
        collectedAt: order.deliveryDate ?? DateTime.now(),
      );

      await _firestore
          .collection('payment_records')
          .doc(order.id)
          .set(paymentRecord.toMap());

      if (kDebugMode) {
        print('Payment record created for order ${order.id}');
      }
    } catch (e) {
      if (e is PaymentException) rethrow;
      throw PaymentException('Failed to create payment record: $e');
    }
  }

  // Confirm cash handover to admin
  Future<void> confirmCashHandover(String orderId, String pin) async {
    try {
      if (!_authService.isAuthenticated) {
        throw PaymentException('Authentication required');
      }

      // Verify PIN (you can customize this logic)
      if (!await _verifyAdminPin(pin)) {
        throw PaymentException('Invalid PIN');
      }

      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        throw PaymentException('User not authenticated');
      }

      await _firestore
          .collection('payment_records')
          .doc(orderId)
          .update({
            'status': PaymentCollectionStatus.handedToAdmin.name,
            'handedOverAt': FieldValue.serverTimestamp(),
            'handedOverBy': currentUser.uid,
          });

      if (kDebugMode) {
        print('Cash handover confirmed for order $orderId');
      }
    } catch (e) {
      if (e is PaymentException) rethrow;
      throw PaymentException('Failed to confirm cash handover: $e');
    }
  }

  // Get payment statistics
  Future<Map<String, dynamic>> getPaymentStatistics() async {
    try {
      final now = DateTime.now();
      final startOfDay = DateTime(now.year, now.month, now.day);
      final startOfMonth = DateTime(now.year, now.month, 1);

      // Get today's cash collections
      final todayCashQuery = await _firestore
          .collection('payment_records')
          .where('paymentMethod', isEqualTo: 'cash')
          .where('status', isEqualTo: PaymentCollectionStatus.collectedByDelivery.name)
          .where('collectedAt', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .get();

      // Get pending cash handovers
      final pendingCashQuery = await _firestore
          .collection('payment_records')
          .where('paymentMethod', isEqualTo: 'cash')
          .where('status', isEqualTo: PaymentCollectionStatus.collectedByDelivery.name)
          .get();

      // Get today's UPI collections
      final todayUpiQuery = await _firestore
          .collection('payment_records')
          .where('paymentMethod', isEqualTo: 'upi')
          .where('status', isEqualTo: PaymentCollectionStatus.upiReceived.name)
          .where('collectedAt', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .get();

      // Get all UPI collections
      final allUpiQuery = await _firestore
          .collection('payment_records')
          .where('paymentMethod', isEqualTo: 'upi')
          .where('status', isEqualTo: PaymentCollectionStatus.upiReceived.name)
          .get();

      // Get this month's total collections
      final monthlyQuery = await _firestore
          .collection('payment_records')
          .where('collectedAt', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfMonth))
          .get();

      double todayCashAmount = 0;
      double pendingCashAmount = 0;
      double todayUpiAmount = 0;
      double totalUpiAmount = 0;
      double monthlyTotal = 0;

      for (var doc in todayCashQuery.docs) {
        todayCashAmount += (doc.data()['amount'] ?? 0).toDouble();
      }

      for (var doc in pendingCashQuery.docs) {
        pendingCashAmount += (doc.data()['amount'] ?? 0).toDouble();
      }

      for (var doc in todayUpiQuery.docs) {
        todayUpiAmount += (doc.data()['amount'] ?? 0).toDouble();
      }

      for (var doc in allUpiQuery.docs) {
        totalUpiAmount += (doc.data()['amount'] ?? 0).toDouble();
      }

      for (var doc in monthlyQuery.docs) {
        monthlyTotal += (doc.data()['amount'] ?? 0).toDouble();
      }

      return {
        'todayCashCollected': todayCashAmount,
        'pendingCashHandover': pendingCashAmount,
        'pendingCashCount': pendingCashQuery.docs.length,
        'todayUpiReceived': todayUpiAmount,
        'totalUpiReceived': totalUpiAmount,
        'totalUpiCount': allUpiQuery.docs.length,
        'monthlyTotal': monthlyTotal,
      };
    } catch (e) {
      if (kDebugMode) {
        print('Error getting payment statistics: $e');
      }
      return {
        'todayCashCollected': 0.0,
        'pendingCashHandover': 0.0,
        'pendingCashCount': 0,
        'todayUpiReceived': 0.0,
        'totalUpiReceived': 0.0,
        'totalUpiCount': 0,
        'monthlyTotal': 0.0,
      };
    }
  }

  // Verify admin PIN (customize this based on your requirements)
  Future<bool> _verifyAdminPin(String pin) async {
    try {
      // You can store the PIN in settings or use a default
      // For now, using a simple default PIN
      const defaultPin = '1234';
      
      // You could also fetch from settings:
      // final settings = await _firestore.collection('settings').doc('admin').get();
      // final storedPin = settings.data()?['pin'] ?? defaultPin;
      
      return pin == defaultPin;
    } catch (e) {
      if (kDebugMode) {
        print('Error verifying PIN: $e');
      }
      return false;
    }
  }

  // Get payment record by order ID
  Future<PaymentRecord?> getPaymentRecord(String orderId) async {
    try {
      final doc = await _firestore.collection('payment_records').doc(orderId).get();
      
      if (doc.exists) {
        return PaymentRecord.fromFirestore(doc);
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting payment record: $e');
      }
      return null;
    }
  }
}
