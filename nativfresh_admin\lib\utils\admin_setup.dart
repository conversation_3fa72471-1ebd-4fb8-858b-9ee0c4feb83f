import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

/// Utility class for setting up admin users and initial data
class AdminSetup {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Creates an admin user with the specified email and password
  /// This should only be used during initial setup
  static Future<void> createAdminUser({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      // Create user account
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      final user = userCredential.user;
      if (user == null) {
        throw Exception('Failed to create user');
      }

      // Update display name
      await user.updateDisplayName(name);

      // Create admin user document in Firestore
      await _firestore.collection('admin_users').doc(user.uid).set({
        'email': email,
        'name': name,
        'role': 'admin',
        'isActive': true,
        'createdAt': FieldValue.serverTimestamp(),
        'lastLogin': null,
        'permissions': [
          'products.read',
          'products.write',
          'products.delete',
          'orders.read',
          'orders.write',
          'orders.delete',
          'users.read',
          'settings.read',
          'settings.write',
        ],
      });

      if (kDebugMode) {
        print('Admin user created successfully: $email');
        print('User ID: ${user.uid}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error creating admin user: $e');
      }
      rethrow;
    }
  }

  /// Sets up initial product categories
  static Future<void> setupInitialCategories() async {
    try {
      final categories = [
        {
          'name': 'Fruits',
          'description': 'Fresh fruits and seasonal produce',
          'imageUrl': '',
          'isActive': true,
          'sortOrder': 1,
          'createdAt': FieldValue.serverTimestamp(),
        },
        {
          'name': 'Vegetables',
          'description': 'Fresh vegetables and leafy greens',
          'imageUrl': '',
          'isActive': true,
          'sortOrder': 2,
          'createdAt': FieldValue.serverTimestamp(),
        },
        {
          'name': 'Dairy',
          'description': 'Milk, cheese, yogurt and dairy products',
          'imageUrl': '',
          'isActive': true,
          'sortOrder': 3,
          'createdAt': FieldValue.serverTimestamp(),
        },
        {
          'name': 'Grains',
          'description': 'Rice, wheat, pulses and cereals',
          'imageUrl': '',
          'isActive': true,
          'sortOrder': 4,
          'createdAt': FieldValue.serverTimestamp(),
        },
        {
          'name': 'Spices',
          'description': 'Spices, herbs and seasonings',
          'imageUrl': '',
          'isActive': true,
          'sortOrder': 5,
          'createdAt': FieldValue.serverTimestamp(),
        },
      ];

      final batch = _firestore.batch();
      for (final category in categories) {
        final docRef = _firestore.collection('categories').doc();
        batch.set(docRef, category);
      }

      await batch.commit();

      if (kDebugMode) {
        print('Initial categories created successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error creating initial categories: $e');
      }
      rethrow;
    }
  }

  /// Sets up initial app settings
  static Future<void> setupInitialSettings() async {
    try {
      await _firestore.collection('app_settings').doc('general').set({
        'deliveryFee': 25.0,
        'minimumOrderAmount': 200.0,
        'maxDeliveryDistance': 10.0,
        'operatingHours': {
          'start': '08:00',
          'end': '20:00',
        },
        'isDeliveryActive': true,
        'supportPhone': '+91-XXXXXXXXXX',
        'supportEmail': '<EMAIL>',
        'appVersion': '1.0.0',
        'maintenanceMode': false,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      await _firestore.collection('app_settings').doc('delivery_areas').set({
        'areas': [
          {
            'name': 'Area 1',
            'pincode': '560001',
            'isActive': true,
            'deliveryFee': 25.0,
          },
          {
            'name': 'Area 2',
            'pincode': '560002',
            'isActive': true,
            'deliveryFee': 30.0,
          },
        ],
        'updatedAt': FieldValue.serverTimestamp(),
      });

      if (kDebugMode) {
        print('Initial app settings created successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error creating initial settings: $e');
      }
      rethrow;
    }
  }

  /// Sets up sample products for testing
  static Future<void> setupSampleProducts(String adminUserId) async {
    try {
      final products = [
        {
          'name': 'Fresh Red Apples',
          'price': 120.0,
          'description': 'Fresh red apples from local farms. Sweet and crispy.',
          'category': 'Fruits',
          'imageUrl': '',
          'isAvailable': true,
          'stockQuantity': 50,
          'unit': 'kg',
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
          'createdBy': adminUserId,
        },
        {
          'name': 'Organic Bananas',
          'price': 80.0,
          'description': 'Organic bananas, perfect for smoothies and snacks.',
          'category': 'Fruits',
          'imageUrl': '',
          'isAvailable': true,
          'stockQuantity': 30,
          'unit': 'dozen',
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
          'createdBy': adminUserId,
        },
        {
          'name': 'Fresh Spinach',
          'price': 40.0,
          'description': 'Fresh green spinach leaves, rich in iron and vitamins.',
          'category': 'Vegetables',
          'imageUrl': '',
          'isAvailable': true,
          'stockQuantity': 25,
          'unit': 'bunch',
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
          'createdBy': adminUserId,
        },
        {
          'name': 'Whole Milk',
          'price': 60.0,
          'description': 'Fresh whole milk from local dairy farms.',
          'category': 'Dairy',
          'imageUrl': '',
          'isAvailable': true,
          'stockQuantity': 40,
          'unit': 'liter',
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
          'createdBy': adminUserId,
        },
      ];

      final batch = _firestore.batch();
      for (final product in products) {
        final docRef = _firestore.collection('products').doc();
        batch.set(docRef, product);
      }

      await batch.commit();

      if (kDebugMode) {
        print('Sample products created successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error creating sample products: $e');
      }
      rethrow;
    }
  }

  /// Complete initial setup
  static Future<void> completeInitialSetup({
    required String adminEmail,
    required String adminPassword,
    required String adminName,
  }) async {
    try {
      if (kDebugMode) {
        print('Starting initial setup...');
      }

      // Create admin user
      await createAdminUser(
        email: adminEmail,
        password: adminPassword,
        name: adminName,
      );

      final adminUserId = _auth.currentUser?.uid;
      if (adminUserId == null) {
        throw Exception('Admin user not created properly');
      }

      // Set up initial data
      await setupInitialCategories();
      await setupInitialSettings();
      await setupSampleProducts(adminUserId);

      if (kDebugMode) {
        print('Initial setup completed successfully!');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error during initial setup: $e');
      }
      rethrow;
    }
  }

  /// Check if initial setup is required
  static Future<bool> isInitialSetupRequired() async {
    try {
      final adminUsers = await _firestore
          .collection('admin_users')
          .where('role', isEqualTo: 'admin')
          .limit(1)
          .get();

      return adminUsers.docs.isEmpty;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking setup status: $e');
      }
      return true; // Assume setup is required if we can't check
    }
  }
}
