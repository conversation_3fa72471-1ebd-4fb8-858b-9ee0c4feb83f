import 'package:flutter/foundation.dart';
import 'dart:developer' as developer;

/// Environment configuration for the application
class Environment {
  // Environment type
  static const String _environment = String.fromEnvironment(
    'ENVIRONMENT',
    defaultValue: 'development',
  );

  // Firebase configuration
  static const String firebaseApiKey = String.fromEnvironment(
    'FIREBASE_API_KEY',
    defaultValue: 'AIzaSyAGMRayUL9zAB95GB7Xb5uSmFP_4AokqjE',
  );

  static const String firebaseAppId = String.fromEnvironment(
    'FIREBASE_WEB_APP_ID',
    defaultValue: '1:841436133900:web:ff5b21d6c7f49ee59e0c29',
  );

  static const String firebaseProjectId = String.fromEnvironment(
    'FIREBASE_PROJECT_ID',
    defaultValue: 'nativfresh-e5b0f',
  );

  static const String firebaseMessagingSenderId = String.fromEnvironment(
    'FIREBASE_MESSAGING_SENDER_ID',
    defaultValue: '841436133900',
  );

  static const String firebaseAuthDomain = String.fromEnvironment(
    'FIREBASE_AUTH_DOMAIN',
    defaultValue: 'nativfresh-e5b0f.firebaseapp.com',
  );

  static const String firebaseStorageBucket = String.fromEnvironment(
    'FIREBASE_STORAGE_BUCKET',
    defaultValue: 'nativfresh-e5b0f.firebasestorage.app',
  );

  static const String firebaseMeasurementId = String.fromEnvironment(
    'FIREBASE_MEASUREMENT_ID',
    defaultValue: 'G-ABC123DEF456',
  );

  // App configuration
  static const String appName = String.fromEnvironment(
    'APP_NAME',
    defaultValue: 'Nativfresh Admin',
  );

  static const String appVersion = String.fromEnvironment(
    'APP_VERSION',
    defaultValue: '1.0.0',
  );

  static const String supportEmail = String.fromEnvironment(
    'SUPPORT_EMAIL',
    defaultValue: '<EMAIL>',
  );

  static const String supportPhone = String.fromEnvironment(
    'SUPPORT_PHONE',
    defaultValue: '+91-XXXXXXXXXX',
  );

  // API configuration
  static const String apiBaseUrl = String.fromEnvironment(
    'API_BASE_URL',
    defaultValue: 'https://api.nativfresh.com',
  );

  static const int apiTimeoutSeconds = int.fromEnvironment(
    'API_TIMEOUT_SECONDS',
    defaultValue: 30,
  );

  // Feature flags
  static const bool enableAnalytics = bool.fromEnvironment(
    'ENABLE_ANALYTICS',
    defaultValue: true,
  );

  static const bool enableCrashlytics = bool.fromEnvironment(
    'ENABLE_CRASHLYTICS',
    defaultValue: true,
  );

  static const bool enablePerformanceMonitoring = bool.fromEnvironment(
    'ENABLE_PERFORMANCE_MONITORING',
    defaultValue: true,
  );

  static const bool enableDebugLogging = bool.fromEnvironment(
    'ENABLE_DEBUG_LOGGING',
    defaultValue: kDebugMode,
  );

  // Security configuration
  static const int maxLoginAttempts = int.fromEnvironment(
    'MAX_LOGIN_ATTEMPTS',
    defaultValue: 10,
  );

  static const int sessionTimeoutMinutes = int.fromEnvironment(
    'SESSION_TIMEOUT_MINUTES',
    defaultValue: 60,
  );

  static const int passwordMinLength = int.fromEnvironment(
    'PASSWORD_MIN_LENGTH',
    defaultValue: 8,
  );

  // File upload configuration
  static const int maxImageSizeMB = int.fromEnvironment(
    'MAX_IMAGE_SIZE_MB',
    defaultValue: 10,
  );

  static const int maxImageWidth = int.fromEnvironment(
    'MAX_IMAGE_WIDTH',
    defaultValue: 1200,
  );

  static const int maxImageHeight = int.fromEnvironment(
    'MAX_IMAGE_HEIGHT',
    defaultValue: 1200,
  );

  static const int imageQuality = int.fromEnvironment(
    'IMAGE_QUALITY',
    defaultValue: 85,
  );

  // Computed properties
  static bool get isProduction => _environment == 'production';
  static bool get isDevelopment => _environment == 'development';
  static bool get isStaging => _environment == 'staging';
  static bool get isTesting => _environment == 'testing';

  static String get environmentName => _environment;

  static bool get isDebugMode => kDebugMode && enableDebugLogging;

  // Validation
  static bool get isValidConfiguration {
    return firebaseProjectId.isNotEmpty &&
           firebaseApiKey.isNotEmpty &&
           firebaseAppId.isNotEmpty &&
           !firebaseAppId.contains('YOUR_') &&
           !firebaseApiKey.contains('demo-') &&
           firebaseProjectId == 'nativfresh-e5b0f';
  }

  // Configuration summary for debugging
  static Map<String, dynamic> get configSummary => {
    'environment': _environment,
    'isProduction': isProduction,
    'isDevelopment': isDevelopment,
    'projectId': firebaseProjectId,
    'appVersion': appVersion,
    'enableAnalytics': enableAnalytics,
    'enableCrashlytics': enableCrashlytics,
    'isValidConfiguration': isValidConfiguration,
  };

  // Print configuration (debug only)
  static void printConfiguration() {
    if (isDebugMode) {
      developer.log('=== Environment Configuration ===');
      configSummary.forEach((key, value) {
        developer.log('$key: $value');
      });
      developer.log('================================');
    }
  }

  // Validate configuration and throw if invalid
  static void validateConfiguration() {
    if (!isValidConfiguration) {
      throw Exception(
        'Invalid Firebase configuration detected. '
        'Please ensure all Firebase credentials are properly set. '
        'Check firebase_options.dart and environment variables.',
      );
    }

    if (isProduction) {
      if (firebaseAppId.contains('YOUR_') ||
          firebaseApiKey.contains('demo-') ||
          firebaseMeasurementId.contains('XXXXXXXXXX') ||
          firebaseAppId.contains('abc123def456789')) {
        throw Exception(
          'Production environment detected with placeholder Firebase credentials. '
          'Please configure proper production Firebase credentials.',
        );
      }
    }
  }
}
