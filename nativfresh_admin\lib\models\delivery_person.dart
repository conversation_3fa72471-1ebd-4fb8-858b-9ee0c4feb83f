import 'package:cloud_firestore/cloud_firestore.dart';

class DeliveryPerson {
  final String id;
  final String name;
  final String phoneNumber;
  final String email;
  final String username;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  DeliveryPerson({
    required this.id,
    required this.name,
    required this.phoneNumber,
    required this.email,
    required this.username,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory DeliveryPerson.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return DeliveryPerson(
      id: doc.id,
      name: data['name'] ?? '',
      phoneNumber: data['phoneNumber'] ?? '',
      email: data['email'] ?? '',
      username: data['username'] ?? '',
      isActive: data['isActive'] ?? true,
      createdAt: _parseTimestamp(data['createdAt']),
      updatedAt: _parseTimestamp(data['updatedAt']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'phoneNumber': phoneNumber,
      'email': email,
      'username': username,
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  static DateTime _parseTimestamp(dynamic timestamp) {
    if (timestamp is Timestamp) {
      return timestamp.toDate();
    }
    if (timestamp is DateTime) {
      return timestamp;
    }
    return DateTime.now();
  }

  DeliveryPerson copyWith({
    String? id,
    String? name,
    String? phoneNumber,
    String? email,
    String? username,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DeliveryPerson(
      id: id ?? this.id,
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      username: username ?? this.username,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String get statusText => isActive ? 'Active' : 'Inactive';
}
