import 'dart:async';
import 'package:flutter/material.dart';

/// Simplified in-memory notification service for delivery app
class SimpleNotificationService {
  static final SimpleNotificationService _instance = SimpleNotificationService._internal();
  factory SimpleNotificationService() => _instance;
  SimpleNotificationService._internal();

  final List<DeliveryNotification> _activeNotifications = [];
  final StreamController<List<DeliveryNotification>> _notificationController = 
      StreamController<List<DeliveryNotification>>.broadcast();

  /// Stream of active notifications
  Stream<List<DeliveryNotification>> get notificationStream => _notificationController.stream;

  /// Initialize notification service
  void initialize() {
    // Add welcome notification
    _addWelcomeNotification();
  }

  /// Dispose resources
  void dispose() {
    _notificationController.close();
  }

  /// Add notification to queue (in-memory only)
  void addNotification(DeliveryNotification notification) {
    // Check for duplicates
    if (!_activeNotifications.any((n) => n.id == notification.id)) {
      _activeNotifications.add(notification);
      _notificationController.add(List.from(_activeNotifications));
      
      // Auto-remove low priority notifications after 30 seconds
      if (notification.priority == NotificationPriority.low) {
        Timer(const Duration(seconds: 30), () {
          removeNotification(notification.id);
        });
      }
    }
  }

  /// Remove notification from memory
  void removeNotification(String notificationId) {
    _activeNotifications.removeWhere((n) => n.id == notificationId);
    _notificationController.add(List.from(_activeNotifications));
  }

  /// Clear all notifications (in-memory only)
  void clearAllNotifications() {
    _activeNotifications.clear();
    _notificationController.add(List.from(_activeNotifications));
  }

  /// Add welcome notification
  void _addWelcomeNotification() {
    final notification = DeliveryNotification(
      id: 'welcome_${DateTime.now().millisecondsSinceEpoch}',
      type: DeliveryNotificationType.systemMessage,
      title: 'Welcome Back!',
      message: 'Ready to start delivering? Check your assigned orders below.',
      priority: NotificationPriority.low,
      createdAt: DateTime.now(),
    );
    addNotification(notification);
  }

  /// Create new order notification
  void notifyNewOrder(String orderNumber, String customerName, double amount) {
    final notification = DeliveryNotification(
      id: 'new_order_${DateTime.now().millisecondsSinceEpoch}',
      type: DeliveryNotificationType.newOrder,
      title: 'New Order Assigned',
      message: 'Order $orderNumber for $customerName - ₹${amount.toStringAsFixed(2)}',
      priority: NotificationPriority.high,
      createdAt: DateTime.now(),
    );
    addNotification(notification);
  }

  /// Create order update notification
  void notifyOrderUpdate(String orderNumber, String status) {
    final notification = DeliveryNotification(
      id: 'update_${DateTime.now().millisecondsSinceEpoch}',
      type: DeliveryNotificationType.orderUpdate,
      title: 'Order Status Updated',
      message: 'Order $orderNumber is now $status',
      priority: NotificationPriority.medium,
      createdAt: DateTime.now(),
    );
    addNotification(notification);
  }

  /// Create payment reminder notification
  void notifyPaymentReminder(String orderNumber, double amount) {
    final notification = DeliveryNotification(
      id: 'payment_${DateTime.now().millisecondsSinceEpoch}',
      type: DeliveryNotificationType.paymentReminder,
      title: 'Payment Collection',
      message: 'Collect ₹${amount.toStringAsFixed(2)} for order $orderNumber',
      priority: NotificationPriority.high,
      createdAt: DateTime.now(),
    );
    addNotification(notification);
  }

  /// Create delivery success notification
  void notifyDeliverySuccess(String orderNumber, String customerName) {
    final notification = DeliveryNotification(
      id: 'success_${DateTime.now().millisecondsSinceEpoch}',
      type: DeliveryNotificationType.deliverySuccess,
      title: 'Delivery Completed',
      message: 'Order $orderNumber delivered to $customerName successfully!',
      priority: NotificationPriority.medium,
      createdAt: DateTime.now(),
    );
    addNotification(notification);
  }

  /// Create system message notification
  void notifySystemMessage(String title, String message, {NotificationPriority priority = NotificationPriority.medium}) {
    final notification = DeliveryNotification(
      id: 'system_${DateTime.now().millisecondsSinceEpoch}',
      type: DeliveryNotificationType.systemMessage,
      title: title,
      message: message,
      priority: priority,
      createdAt: DateTime.now(),
    );
    addNotification(notification);
  }

  /// Show popup notification
  static void showPopupNotification(
    BuildContext context, {
    required String title,
    required String message,
    IconData? icon,
    Color? backgroundColor,
    Duration duration = const Duration(seconds: 4),
  }) {
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).padding.top + 10,
        left: 16,
        right: 16,
        child: Material(
          color: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: backgroundColor ?? Colors.green.shade600,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                if (icon != null) ...[
                  Icon(icon, color: Colors.white, size: 24),
                  const SizedBox(width: 12),
                ],
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        message,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => overlayEntry.remove(),
                  icon: const Icon(Icons.close, color: Colors.white),
                ),
              ],
            ),
          ),
        ),
      ),
    );

    overlay.insert(overlayEntry);

    // Auto remove after duration
    Future.delayed(duration, () {
      if (overlayEntry.mounted) {
        overlayEntry.remove();
      }
    });
  }

  /// Get notification count
  int get notificationCount => _activeNotifications.length;

  /// Get unread high priority notifications
  List<DeliveryNotification> get highPriorityNotifications => 
      _activeNotifications.where((n) => n.priority == NotificationPriority.high).toList();
}

/// Delivery notification types
enum DeliveryNotificationType {
  newOrder,
  orderUpdate,
  paymentReminder,
  deliverySuccess,
  systemMessage,
}

enum NotificationPriority {
  low,
  medium,
  high,
  critical,
}

class DeliveryNotification {
  final String id;
  final DeliveryNotificationType type;
  final String title;
  final String message;
  final NotificationPriority priority;
  final DateTime createdAt;

  DeliveryNotification({
    required this.id,
    required this.type,
    required this.title,
    required this.message,
    required this.priority,
    required this.createdAt,
  });
}
