import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import 'dart:async';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  // Rate limiting - more lenient for admin app
  final Map<String, List<DateTime>> _attemptHistory = {};
  static const int maxAttemptsPerWindow = 10;
  static const Duration rateLimitWindow = Duration(minutes: 5);

  // Current user stream
  Stream<User?> get authStateChanges => _auth.authStateChanges();
  
  // Get current user
  User? get currentUser => _auth.currentUser;
  
  // Check if user is authenticated
  bool get isAuthenticated => currentUser != null;

  // Sign in with email and password
  Future<UserCredential> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      // Input validation
      _validateEmail(email);
      _validatePassword(password);
      
      // Rate limiting - disabled for admin app to allow more flexibility
      // await _checkRateLimit(email);
      
      // Attempt sign in
      final credential = await _auth.signInWithEmailAndPassword(
        email: email.trim().toLowerCase(),
        password: password,
      );

      if (kDebugMode) {
        print('User signed in successfully: ${credential.user?.email}');
      }

      // Add a small delay to ensure Firestore document is available
      await Future.delayed(const Duration(milliseconds: 500));

      // Skip admin verification during login - will be checked by AuthWrapper
      if (kDebugMode) {
        print('Login successful - admin verification will be done by AuthWrapper');
      }

      // Save login timestamp
      await _saveLoginTimestamp();

      return credential;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw AuthException('An unexpected error occurred: $e');
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _auth.signOut();
      await _clearLocalData();
    } catch (e) {
      throw AuthException('Failed to sign out: $e');
    }
  }

  // Reset password
  Future<void> resetPassword(String email) async {
    try {
      _validateEmail(email);
      await _auth.sendPasswordResetEmail(email: email.trim().toLowerCase());
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw AuthException('Failed to send reset email: $e');
    }
  }

  // Check if user has admin role
  Future<bool> isAdmin() async {
    try {
      final user = currentUser;
      if (user == null) {
        if (kDebugMode) {
          print('isAdmin: No current user');
        }
        return false;
      }

      if (kDebugMode) {
        print('isAdmin: Checking for user ${user.email}');
      }

      // First check Firestore admin_users collection
      try {
        final adminDoc = await _firestore
            .collection('admin_users')
            .doc(user.uid)
            .get();

        if (kDebugMode) {
          print('isAdmin: Admin doc exists: ${adminDoc.exists}');
          if (adminDoc.exists) {
            print('isAdmin: Admin doc data: ${adminDoc.data()}');
          }
        }

        if (adminDoc.exists && adminDoc.data()?['role'] == 'admin') {
          if (kDebugMode) {
            print('isAdmin: Verified via Firestore - TRUE');
          }
          return true;
        }
      } catch (firestoreError) {
        if (kDebugMode) {
          print('isAdmin: Firestore error: $firestoreError');
        }
      }

      // Check custom claims as fallback
      try {
        final idTokenResult = await user.getIdTokenResult();
        final isAdminClaim = idTokenResult.claims?['role'] == 'admin';

        if (kDebugMode) {
          print('isAdmin: Custom claims check: $isAdminClaim');
        }

        if (isAdminClaim) {
          if (kDebugMode) {
            print('isAdmin: Verified via custom claims - TRUE');
          }
          return true;
        }
      } catch (claimsError) {
        if (kDebugMode) {
          print('isAdmin: Custom claims error: $claimsError');
        }
      }

      if (kDebugMode) {
        print('isAdmin: No admin role found - FALSE');
        print('isAdmin: User UID: ${user.uid}');
        print('isAdmin: User email: ${user.email}');
        print('isAdmin: User display name: ${user.displayName}');
      }
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('isAdmin: General error: $e');
      }
      return false;
    }
  }

  // Get user profile
  Future<Map<String, dynamic>?> getUserProfile() async {
    try {
      final user = currentUser;
      if (user == null) return null;
      
      final doc = await _firestore
          .collection('admin_users')
          .doc(user.uid)
          .get();
      
      return doc.data();
    } catch (e) {
      throw AuthException('Failed to get user profile: $e');
    }
  }

  // Update user profile
  Future<void> updateUserProfile(Map<String, dynamic> data) async {
    try {
      final user = currentUser;
      if (user == null) throw AuthException('User not authenticated');
      
      await _firestore
          .collection('admin_users')
          .doc(user.uid)
          .update({
        ...data,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw AuthException('Failed to update profile: $e');
    }
  }

  // Private methods
  void _validateEmail(String email) {
    if (email.trim().isEmpty) {
      throw AuthException('Email is required');
    }
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(email.trim())) {
      throw AuthException('Please enter a valid email address');
    }
  }

  void _validatePassword(String password) {
    if (password.isEmpty) {
      throw AuthException('Password is required');
    }
    
    if (password.length < 6) {
      throw AuthException('Password must be at least 6 characters long');
    }
  }

  Future<void> _checkRateLimit(String email) async {
    final now = DateTime.now();
    final key = email.toLowerCase();

    // Get or create attempt history for this email
    if (!_attemptHistory.containsKey(key)) {
      _attemptHistory[key] = [];
    }

    final attempts = _attemptHistory[key]!;

    // Remove attempts older than the rate limit window
    attempts.removeWhere((attempt) =>
      now.difference(attempt) > rateLimitWindow);

    // Check if we've exceeded the maximum attempts
    if (attempts.length >= maxAttemptsPerWindow) {
      final oldestAttempt = attempts.first;
      final timeUntilReset = rateLimitWindow - now.difference(oldestAttempt);

      throw AuthException(
        'Too many login attempts. Please wait ${timeUntilReset.inMinutes + 1} minutes before trying again.'
      );
    }

    // Add current attempt
    attempts.add(now);
  }



  Future<void> _saveLoginTimestamp() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('last_login', DateTime.now().toIso8601String());
    } catch (e) {
      // Non-critical error, don't throw
      if (kDebugMode) {
        print('Failed to save login timestamp: $e');
      }
    }
  }

  Future<void> _clearLocalData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('last_login');
      // Clear any other cached data
    } catch (e) {
      // Non-critical error, don't throw
      if (kDebugMode) {
        print('Failed to clear local data: $e');
      }
    }
  }

  AuthException _handleAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return AuthException('No account found with this email address.');
      case 'wrong-password':
        return AuthException('Incorrect password. Please try again.');
      case 'invalid-email':
        return AuthException('Please enter a valid email address.');
      case 'user-disabled':
        return AuthException('This account has been disabled.');
      case 'too-many-requests':
        return AuthException('Too many failed attempts. Please try again later.');
      case 'network-request-failed':
        return AuthException('Network error. Please check your connection.');
      case 'invalid-credential':
        return AuthException('Invalid email or password.');
      default:
        return AuthException(e.message ?? 'Authentication failed.');
    }
  }
}

// Custom exception class
class AuthException implements Exception {
  final String message;
  
  AuthException(this.message);
  
  @override
  String toString() => message;
}

// Auth state provider for UI
class AuthStateProvider {
  static final AuthStateProvider _instance = AuthStateProvider._internal();
  factory AuthStateProvider() => _instance;
  AuthStateProvider._internal();

  final AuthService _authService = AuthService();
  final StreamController<AuthState> _stateController = StreamController<AuthState>.broadcast();

  Stream<AuthState> get authStateStream => _stateController.stream;
  AuthState _currentState = AuthState.unknown;
  
  AuthState get currentState => _currentState;

  void initialize() {
    _authService.authStateChanges.listen((user) async {
      if (user == null) {
        _updateState(AuthState.unauthenticated);
      } else {
        try {
          final isAdmin = await _authService.isAdmin();
          if (isAdmin) {
            _updateState(AuthState.authenticated);
          } else {
            await _authService.signOut();
            _updateState(AuthState.unauthorized);
          }
        } catch (e) {
          _updateState(AuthState.error);
        }
      }
    });
  }

  void _updateState(AuthState newState) {
    _currentState = newState;
    _stateController.add(newState);
  }

  void dispose() {
    _stateController.close();
  }
}

enum AuthState {
  unknown,
  authenticated,
  unauthenticated,
  unauthorized,
  error,
}
