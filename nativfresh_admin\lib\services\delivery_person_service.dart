import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import '../models/delivery_person.dart';
import '../utils/username_generator.dart';
import 'auth_service.dart';

class DeliveryPersonException implements Exception {
  final String message;
  DeliveryPersonException(this.message);
  
  @override
  String toString() => message;
}

class DeliveryPersonService {
  static final DeliveryPersonService _instance = DeliveryPersonService._internal();
  factory DeliveryPersonService() => _instance;
  DeliveryPersonService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final AuthService _authService = AuthService();

  // Get all delivery persons
  Stream<List<DeliveryPerson>> getDeliveryPersons() {
    return _firestore
        .collection('delivery_persons')
        .orderBy('name')
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => DeliveryPerson.fromFirestore(doc))
            .toList());
  }

  // Get active delivery persons only
  Stream<List<DeliveryPerson>> getActiveDeliveryPersons() {
    return _firestore
        .collection('delivery_persons')
        .where('isActive', isEqualTo: true)
        .orderBy('name')
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => DeliveryPerson.fromFirestore(doc))
            .toList());
  }

  // Get specific delivery person
  Future<DeliveryPerson?> getDeliveryPerson(String id) async {
    try {
      final doc = await _firestore.collection('delivery_persons').doc(id).get();
      if (doc.exists) {
        return DeliveryPerson.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting delivery person: $e');
      }
      throw DeliveryPersonException('Failed to get delivery person: $e');
    }
  }

  // Create new delivery person
  Future<String> createDeliveryPerson({
    required String name,
    required String phoneNumber,
    required String email,
  }) async {
    try {
      if (!_authService.isAuthenticated) {
        throw DeliveryPersonException('Authentication required');
      }

      // Check if email already exists
      final existingUser = await _firestore
          .collection('delivery_persons')
          .where('email', isEqualTo: email)
          .get();

      if (existingUser.docs.isNotEmpty) {
        throw DeliveryPersonException('Email already exists');
      }

      // Generate unique username
      final existingUsernames = await _getExistingUsernames();
      final username = UsernameGenerator.generateUnique(existingUsernames);

      // Create Firebase Auth user with username as email and default password
      final usernameEmail = '$<EMAIL>';
      final defaultPassword = 'nativ123';

      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: usernameEmail,
        password: defaultPassword,
      );

      if (userCredential.user == null) {
        throw DeliveryPersonException('Failed to create Firebase Auth user');
      }

      final userId = userCredential.user!.uid;
      final now = DateTime.now();

      // Create delivery person document
      final deliveryPerson = DeliveryPerson(
        id: userId,
        name: name,
        phoneNumber: phoneNumber,
        email: email,
        username: username,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      );

      // Create the delivery person document
      final deliveryPersonData = deliveryPerson.toMap();
      deliveryPersonData['firebaseEmail'] = usernameEmail; // Store the Firebase email for reference

      await _firestore
          .collection('delivery_persons')
          .doc(userId)
          .set(deliveryPersonData);

      if (kDebugMode) {
        print('Delivery person created successfully: $userId with username: $username');
        print('Firebase Auth email: $usernameEmail with default password: $defaultPassword');
      }

      return userId;
    } catch (e) {
      if (e is DeliveryPersonException) rethrow;
      if (e is FirebaseAuthException) {
        throw DeliveryPersonException(_handleFirebaseAuthException(e));
      }
      throw DeliveryPersonException('Failed to create delivery person: $e');
    }
  }

  // Update delivery person
  Future<void> updateDeliveryPerson(String id, {
    String? name,
    String? phoneNumber,
    String? email,
    bool? isActive,
    String? password,
  }) async {
    try {
      if (!_authService.isAuthenticated) {
        throw DeliveryPersonException('Authentication required');
      }

      final updateData = <String, dynamic>{
        'updatedAt': FieldValue.serverTimestamp(),
      };

      if (name != null) updateData['name'] = name;
      if (phoneNumber != null) updateData['phoneNumber'] = phoneNumber;
      if (email != null) updateData['email'] = email;
      if (isActive != null) updateData['isActive'] = isActive;

      // Update Firestore document
      await _firestore
          .collection('delivery_persons')
          .doc(id)
          .update(updateData);

      // Update password if provided
      if (password != null && password.isNotEmpty) {
        await _updateDeliveryPersonPassword(id, password);
      }

      if (kDebugMode) {
        print('Delivery person updated successfully: $id');
      }
    } catch (e) {
      if (e is DeliveryPersonException) rethrow;
      throw DeliveryPersonException('Failed to update delivery person: $e');
    }
  }

  // Update delivery person password directly (admin privilege)
  Future<void> _updateDeliveryPersonPassword(String userId, String newPassword) async {
    try {
      // Get delivery person document to get username
      final deliveryPersonDoc = await _firestore
          .collection('delivery_persons')
          .doc(userId)
          .get();

      if (!deliveryPersonDoc.exists) {
        throw DeliveryPersonException('Delivery person not found');
      }

      // Note: Admin cannot directly update Firebase Auth passwords for other users
      // This would require Firebase Admin SDK or the user to be currently signed in
      // For now, we'll just update the timestamp in Firestore for tracking
      await _firestore
          .collection('delivery_persons')
          .doc(userId)
          .update({
        'passwordUpdatedByAdmin': true,
        'passwordUpdatedAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      if (kDebugMode) {
        print('Password update requested for delivery person: $userId');
        print('Note: Firebase Auth password cannot be updated by admin without Admin SDK');
        print('Delivery person should use the change password feature in the app');
      }

    } catch (e) {
      throw DeliveryPersonException('Failed to update delivery person password: $e');
    }
  }

  // Deactivate delivery person (soft delete - preserves data)
  Future<void> deactivateDeliveryPerson(String id) async {
    try {
      if (!_authService.isAuthenticated) {
        throw DeliveryPersonException('Authentication required');
      }

      await updateDeliveryPerson(id, isActive: false);

      if (kDebugMode) {
        print('Delivery person deactivated: $id');
      }
    } catch (e) {
      if (e is DeliveryPersonException) rethrow;
      throw DeliveryPersonException('Failed to deactivate delivery person: $e');
    }
  }

  // Delete delivery person (hard delete - removes from Firestore and Firebase Auth)
  Future<void> deleteDeliveryPerson(String id) async {
    try {
      if (!_authService.isAuthenticated) {
        throw DeliveryPersonException('Authentication required');
      }

      // Check if delivery person has any assigned orders
      final assignedOrders = await _firestore
          .collection('orders')
          .where('assignedDeliveryPersonId', isEqualTo: id)
          .where('status', whereIn: ['outForDelivery'])
          .get();

      if (assignedOrders.docs.isNotEmpty) {
        throw DeliveryPersonException(
          'Cannot delete delivery person with active deliveries'
        );
      }

      // Check if delivery person document exists
      final deliveryPersonDoc = await _firestore
          .collection('delivery_persons')
          .doc(id)
          .get();

      if (!deliveryPersonDoc.exists) {
        throw DeliveryPersonException('Delivery person not found');
      }

      // Delete from Firestore first
      await _firestore
          .collection('delivery_persons')
          .doc(id)
          .delete();

      // Note: Firebase Auth user deletion requires admin SDK or the user to be currently signed in
      // Since we're using individual Firebase Auth accounts now, we should ideally delete them
      // For now, we'll delete from Firestore which prevents login

      if (kDebugMode) {
        print('Delivery person deleted from Firestore: $id');
        print('Note: Firebase Auth user still exists but cannot access app without Firestore document');
      }
    } catch (e) {
      if (e is DeliveryPersonException) rethrow;
      throw DeliveryPersonException('Failed to delete delivery person: $e');
    }
  }



  // Get delivery statistics for a specific delivery person
  Future<Map<String, int>> getDeliveryPersonStats(String deliveryPersonId) async {
    try {
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      final startOfMonth = DateTime(today.year, today.month, 1);

      // Get total deliveries
      final totalQuery = await _firestore
          .collection('orders')
          .where('assignedDeliveryPersonId', isEqualTo: deliveryPersonId)
          .where('status', isEqualTo: 'delivered')
          .get();

      // Get today's deliveries
      final todayQuery = await _firestore
          .collection('orders')
          .where('assignedDeliveryPersonId', isEqualTo: deliveryPersonId)
          .where('status', isEqualTo: 'delivered')
          .where('deliveryDate', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .get();

      // Get this month's deliveries
      final monthQuery = await _firestore
          .collection('orders')
          .where('assignedDeliveryPersonId', isEqualTo: deliveryPersonId)
          .where('status', isEqualTo: 'delivered')
          .where('deliveryDate', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfMonth))
          .get();

      // Get pending deliveries
      final pendingQuery = await _firestore
          .collection('orders')
          .where('assignedDeliveryPersonId', isEqualTo: deliveryPersonId)
          .where('status', isEqualTo: 'outForDelivery')
          .get();

      return {
        'total': totalQuery.docs.length,
        'today': todayQuery.docs.length,
        'thisMonth': monthQuery.docs.length,
        'pending': pendingQuery.docs.length,
      };
    } catch (e) {
      if (kDebugMode) {
        print('Error getting delivery person stats: $e');
      }
      return {'total': 0, 'today': 0, 'thisMonth': 0, 'pending': 0};
    }
  }

  // Get all existing usernames to ensure uniqueness
  Future<List<String>> _getExistingUsernames() async {
    try {
      final snapshot = await _firestore
          .collection('delivery_persons')
          .get();

      return snapshot.docs
          .map((doc) => doc.data()['username'] as String?)
          .where((username) => username != null && username.isNotEmpty)
          .cast<String>()
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching existing usernames: $e');
      }
      return [];
    }
  }

  // Handle Firebase Auth exceptions
  String _handleFirebaseAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'email-already-in-use':
        return 'Username already exists';
      case 'invalid-email':
        return 'Invalid username format';
      case 'weak-password':
        return 'Password is too weak';
      case 'network-request-failed':
        return 'Network error. Please check your connection';
      default:
        return 'Authentication error: ${e.message}';
    }
  }
}
