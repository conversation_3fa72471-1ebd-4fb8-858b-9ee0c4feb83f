import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:nativfresh_admin/screens/login_screen.dart';
import 'package:nativfresh_admin/screens/home_screen.dart';
import 'package:nativfresh_admin/services/auth_service.dart';
import 'package:nativfresh_admin/config/environment.dart';
import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Print environment configuration in debug mode
  Environment.printConfiguration();

  bool firebaseInitialized = false;
  try {
    // Validate configuration before initializing Firebase
    Environment.validateConfiguration();

    // Initialize Firebase with proper configuration
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    firebaseInitialized = true;
    if (kDebugMode) {
      print('Firebase initialized successfully for ${Environment.firebaseProjectId}');
    }

    // Auth state will be handled by <PERSON>Builder in AuthWrapper
  } catch (e) {
    if (kDebugMode) {
      print('Firebase initialization error: $e');
      if (Environment.isValidConfiguration) {
        print('Configuration is valid but initialization failed');
      } else {
        print('Invalid configuration detected - running in demo mode');
      }
    }
    // Continue without Firebase for demo purposes
  }

  runApp(MyApp(firebaseInitialized: firebaseInitialized));
}

class MyApp extends StatelessWidget {
  final bool firebaseInitialized;

  const MyApp({super.key, required this.firebaseInitialized});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Nativfresh Admin',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: firebaseInitialized
          ? const InitialSetupChecker()
          : const FirebaseErrorScreen(),
      routes: {
        '/login': (context) => const LoginScreen(),
        '/home': (context) => const HomeScreen(),
      },
    );
  }
}

class InitialSetupChecker extends StatelessWidget {
  const InitialSetupChecker({super.key});

  @override
  Widget build(BuildContext context) {
    // Always show AuthWrapper first, which will show LoginScreen
    // The setup option will be available from the login screen
    return const AuthWrapper();
  }
}

class FirebaseErrorScreen extends StatelessWidget {
  const FirebaseErrorScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Nativfresh Admin - Demo Mode'),
        backgroundColor: Colors.orange,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.warning,
                size: 80,
                color: Colors.orange,
              ),
              const SizedBox(height: 24),
              const Text(
                'Firebase Not Configured',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'Firebase is not properly configured for this web app. '
                'This is normal for a demo environment.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pushReplacement(
                    MaterialPageRoute(
                      builder: (context) => const LoginScreen(),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Continue in Demo Mode'),
              ),
              const SizedBox(height: 16),
              const Text(
                'Demo Mode: Authentication and data storage are simulated',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  bool _isCheckingAdmin = false;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<User?>(
      stream: FirebaseAuth.instance.authStateChanges(),
      builder: (context, snapshot) {
        if (kDebugMode) {
          print('AuthWrapper - Connection state: ${snapshot.connectionState}');
          print('AuthWrapper - Has data: ${snapshot.hasData}');
          print('AuthWrapper - User: ${snapshot.data?.email}');
        }

        // Show loading only briefly while checking auth state
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        // If user is logged in, verify admin role before showing home screen
        if (snapshot.hasData && snapshot.data != null) {
          if (kDebugMode) {
            print('AuthWrapper - User found, checking admin role...');
          }

          return FutureBuilder<bool>(
            future: _checkAdminRole(snapshot.data!),
            builder: (context, adminSnapshot) {
              if (adminSnapshot.connectionState == ConnectionState.waiting) {
                return const Scaffold(
                  body: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Verifying admin access...'),
                      ],
                    ),
                  ),
                );
              }

              if (adminSnapshot.hasData && adminSnapshot.data == true) {
                if (kDebugMode) {
                  print('AuthWrapper - Admin verified, showing HomeScreen');
                }
                return const HomeScreen();
              }

              // Admin verification failed, show login screen
              if (kDebugMode) {
                print('AuthWrapper - Admin verification failed, showing LoginScreen');
              }
              return const LoginScreen();
            },
          );
        }

        // If no user, show login screen
        if (kDebugMode) {
          print('AuthWrapper - No user, showing LoginScreen');
        }
        return const LoginScreen();
      },
    );
  }

  Future<bool> _checkAdminRole(User user) async {
    try {
      if (_isCheckingAdmin) return false;
      _isCheckingAdmin = true;

      if (kDebugMode) {
        print('Checking admin role for: ${user.email}');
      }

      final authService = AuthService();
      final isAdmin = await authService.isAdmin();

      if (kDebugMode) {
        print('Admin check result: $isAdmin');
      }

      return isAdmin;
    } catch (e) {
      if (kDebugMode) {
        print('Admin check error: $e');
      }
      return false;
    } finally {
      _isCheckingAdmin = false;
    }
  }
}