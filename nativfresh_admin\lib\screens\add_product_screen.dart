import 'package:flutter/material.dart';

import 'package:flutter/foundation.dart';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import '../models/product.dart';
import '../services/product_service.dart';

class AddProductScreen extends StatefulWidget {
  const AddProductScreen({super.key});

  @override
  State<AddProductScreen> createState() => _AddProductScreenState();
}

class _AddProductScreenState extends State<AddProductScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _mrpController = TextEditingController();
  final _categoryController = TextEditingController();
  final _stockController = TextEditingController();
  final _productService = ProductService();

  bool _isLoading = false;
  bool _isAvailable = true;
  String? _editingProductId;

  File? _selectedImage;

  List<String> _categories = [];

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _mrpController.dispose();
    _categoryController.dispose();
    _stockController.dispose();
    super.dispose();
  }

  Future<void> _loadCategories() async {
    try {
      final categories = await _productService.getCategories();
      setState(() {
        _categories = categories;
      });
    } catch (e) {
      if (kDebugMode) {
        print('Failed to load categories: $e');
      }
    }
  }

  void _clearForm() {
    _formKey.currentState?.reset();
    _nameController.clear();
    _descriptionController.clear();
    _priceController.clear();
    _categoryController.clear();
    _stockController.clear();
    setState(() {
      _editingProductId = null;
      _selectedImage = null;
      _isAvailable = true;
    });
  }



  Future<void> _saveProduct() async {
    if (kDebugMode) {
      print('_saveProduct: Starting product save process');
    }

    if (!_formKey.currentState!.validate()) {
      if (kDebugMode) {
        print('_saveProduct: Form validation failed');
      }
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final name = _nameController.text.trim();
      final description = _descriptionController.text.trim();
      final price = double.parse(_priceController.text.trim());
      final mrp = double.parse(_mrpController.text.trim());
      final category = _categoryController.text.trim();
      final stockQuantity = int.tryParse(_stockController.text.trim()) ?? 0;

      if (kDebugMode) {
        print('_saveProduct: Product data - Name: $name, Price: $price, Category: $category, Stock: $stockQuantity');
      }

      if (_editingProductId == null) {
        // Add new product
        if (kDebugMode) {
          print('_saveProduct: Adding new product to Firebase...');
        }

        final productId = await _productService.addProduct(
          name: name,
          price: price,
          mrp: mrp,
          description: description,
          category: category,
          imageFile: _selectedImage,
          stockQuantity: stockQuantity,
          isAvailable: _isAvailable,
        );

        if (kDebugMode) {
          print('_saveProduct: Product added successfully with ID: $productId');
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Product added successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        // Update existing product
        await _productService.updateProduct(
          productId: _editingProductId!,
          name: name,
          price: price,
          description: description,
          category: category,
          imageFile: _selectedImage,
          stockQuantity: stockQuantity,
          isAvailable: _isAvailable,
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Product updated successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }

      _clearForm();
      await _loadCategories(); // Refresh categories
    } on ProductException catch (e) {
      if (kDebugMode) {
        print('_saveProduct: ProductException - ${e.message}');
      }
      // Error handled by snackbar display
    } catch (e) {
      if (kDebugMode) {
        print('_saveProduct: Unexpected error - $e');
      }
      // Error handled by snackbar display
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _pickImage() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = File(result.files.first.path!);
        setState(() {
          _selectedImage = file;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to pick image: $e')),
        );
      }
    }
  }





  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_editingProductId == null ? 'Add Product' : 'Edit Product'),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'Product Name',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a product name';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 15),
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Product Description',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a description';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 15),
                // MRP and Price Row
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _mrpController,
                        decoration: const InputDecoration(
                          labelText: 'MRP (₹) *',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.local_offer),
                          helperText: 'Maximum Retail Price',
                        ),
                        keyboardType: TextInputType.number,
                        validator: ProductValidator.validateMrp,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _priceController,
                        decoration: const InputDecoration(
                          labelText: 'Selling Price (₹) *',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.currency_rupee),
                          helperText: 'Your selling price',
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          // First validate the price itself
                          final priceValidation = ProductValidator.validatePrice(value);
                          if (priceValidation != null) return priceValidation;

                          // Then validate price vs MRP
                          final price = double.tryParse(value!);
                          final mrp = double.tryParse(_mrpController.text);
                          if (price != null && mrp != null) {
                            return ProductValidator.validatePriceVsMrp(price, mrp);
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                // Discount Display
                ValueListenableBuilder(
                  valueListenable: _priceController,
                  builder: (context, value, child) {
                    return ValueListenableBuilder(
                      valueListenable: _mrpController,
                      builder: (context, mrpValue, child) {
                        final price = double.tryParse(_priceController.text);
                        final mrp = double.tryParse(_mrpController.text);

                        if (price != null && mrp != null && mrp > price) {
                          final discount = ((mrp - price) / mrp) * 100;
                          final savings = mrp - price;
                          return Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.green.shade50,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.green.shade200),
                            ),
                            child: Row(
                              children: [
                                Icon(Icons.local_offer, color: Colors.green.shade600, size: 20),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    '${discount.toStringAsFixed(1)}% OFF • Save ₹${savings.toStringAsFixed(2)}',
                                    style: TextStyle(
                                      color: Colors.green.shade700,
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    );
                  },
                ),
                const SizedBox(height: 15),
                TextFormField(
                  controller: _categoryController,
                  decoration: InputDecoration(
                    labelText: 'Category',
                    border: const OutlineInputBorder(),
                    suffixIcon: _categories.isNotEmpty
                        ? PopupMenuButton<String>(
                            icon: const Icon(Icons.arrow_drop_down),
                            onSelected: (String value) {
                              _categoryController.text = value;
                            },
                            itemBuilder: (BuildContext context) {
                              return _categories.map((String category) {
                                return PopupMenuItem<String>(
                                  value: category,
                                  child: Text(category),
                                );
                              }).toList();
                            },
                          )
                        : null,
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a category';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 15),
                TextFormField(
                  controller: _stockController,
                  decoration: const InputDecoration(
                    labelText: 'Stock Quantity',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value != null && value.isNotEmpty) {
                      if (int.tryParse(value) == null) {
                        return 'Please enter a valid number';
                      }
                      if (int.parse(value) < 0) {
                        return 'Stock quantity cannot be negative';
                      }
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 15),
                Row(
                  children: [
                    Checkbox(
                      value: _isAvailable,
                      onChanged: (bool? value) {
                        setState(() {
                          _isAvailable = value ?? true;
                        });
                      },
                    ),
                    const Text('Available for sale'),
                  ],
                ),
                const SizedBox(height: 15),
                Container(
                  width: double.infinity,
                  height: 200,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: _selectedImage != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.file(
                            _selectedImage!,
                            fit: BoxFit.cover,
                          ),
                        )
                      : Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.image, size: 64, color: Colors.grey),
                            const SizedBox(height: 8),
                            const Text('No image selected'),
                            const SizedBox(height: 8),
                            ElevatedButton(
                              onPressed: _pickImage,
                              child: const Text('Select Image'),
                            ),
                          ],
                        ),
                ),
                const SizedBox(height: 20),
                _isLoading
                    ? const CircularProgressIndicator()
                    : Column(
                        children: [
                          ElevatedButton(
                            onPressed: _saveProduct,
                            style: ElevatedButton.styleFrom(
                              minimumSize: const Size(double.infinity, 50),
                            ),
                            child: Text(
                              _editingProductId == null ? 'Add Product' : 'Update Product',
                              style: const TextStyle(fontSize: 18),
                            ),
                          ),
                          if (_editingProductId != null)
                            TextButton(
                              onPressed: _clearForm,
                              child: const Text('Cancel Edit'),
                            ),
                        ],
                      ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}