import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/order.dart' as order_model;
import 'auth_service.dart';
import 'payment_service.dart';


class OrderService {
  static final OrderService _instance = OrderService._internal();
  factory OrderService() => _instance;
  OrderService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AuthService _authService = AuthService();
  final PaymentService _paymentService = PaymentService();


  // Get all orders
  Stream<List<order_model.Order>> getOrders() {
    return _firestore
        .collection('orders')
        .orderBy('timestamp', descending: true) // Use timestamp field from user orders
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        try {
          return order_model.Order.fromFirestore(doc);
        } catch (e) {
          if (kDebugMode) {
            print('Error parsing order ${doc.id}: $e');
          }
          return null;
        }
      }).where((order) => order != null).cast<order_model.Order>().toList();
    });
  }

  // Get orders by status
  Stream<List<order_model.Order>> getOrdersByStatus(order_model.OrderStatus status) {
    // For legacy user orders, we'll filter in memory since they don't have status field
    return getOrders().map((orders) {
      return orders.where((order) => order.status == status).toList();
    });
  }

  // Get single order
  Future<order_model.Order?> getOrder(String orderId) async {
    try {
      final doc = await _firestore
          .collection('orders')
          .doc(orderId)
          .get();
      
      if (!doc.exists) return null;
      
      return order_model.Order.fromFirestore(doc);
    } catch (e) {
      throw OrderException('Failed to get order: $e');
    }
  }

  // Update order status
  Future<void> updateOrderStatus(String orderId, order_model.OrderStatus newStatus) async {
    try {
      if (!_authService.isAuthenticated) {
        throw OrderException('Authentication required');
      }

      // Get the current order to check if it's legacy format
      final doc = await _firestore.collection('orders').doc(orderId).get();
      if (!doc.exists) {
        throw OrderException('Order not found');
      }

      final data = doc.data() as Map<String, dynamic>;
      bool isLegacyFormat = data.containsKey('userName') || data.containsKey('userPhone');

      final updateData = <String, dynamic>{
        'status': newStatus.name,
      };

      // For legacy orders, we need to add the updatedAt field since it might not exist
      if (isLegacyFormat) {
        updateData['updatedAt'] = FieldValue.serverTimestamp();
        // Also add createdAt if it doesn't exist (use timestamp field)
        if (!data.containsKey('createdAt') && data.containsKey('timestamp')) {
          updateData['createdAt'] = data['timestamp'];
        }
      } else {
        updateData['updatedAt'] = FieldValue.serverTimestamp();
      }

      // Add delivery date if status is delivered
      if (newStatus == order_model.OrderStatus.delivered) {
        updateData['deliveryDate'] = FieldValue.serverTimestamp();
      }

      if (kDebugMode) {
        print('Updating order $orderId with data: $updateData');
      }

      await _firestore
          .collection('orders')
          .doc(orderId)
          .update(updateData);

      // Create payment record when order is delivered
      if (newStatus == order_model.OrderStatus.delivered) {
        try {
          final updatedOrder = await getOrder(orderId);
          if (updatedOrder != null) {
            await _paymentService.createPaymentRecord(updatedOrder);
          }
        } catch (e) {
          if (kDebugMode) {
            print('Warning: Failed to create payment record for order $orderId: $e');
          }
          // Don't throw error here as order update was successful
        }
      }

      if (kDebugMode) {
        print('Order $orderId status updated to ${newStatus.name} successfully');

        // Verify the update by reading the document back
        final updatedDoc = await _firestore.collection('orders').doc(orderId).get();
        if (updatedDoc.exists) {
          final updatedData = updatedDoc.data() as Map<String, dynamic>;
          print('Verified: Order $orderId now has status: ${updatedData['status']}');
        }
      }
    } catch (e) {
      if (e is OrderException) rethrow;
      throw OrderException('Failed to update order status: $e');
    }
  }

  // Cancel order
  Future<void> cancelOrder(String orderId, String reason) async {
    try {
      if (!_authService.isAuthenticated) {
        throw OrderException('Authentication required');
      }

      final order = await getOrder(orderId);
      if (order == null) {
        throw OrderException('Order not found');
      }

      if (!order.canBeCancelled) {
        throw OrderException('Order cannot be cancelled at this stage');
      }

      // Check if it's legacy format
      final doc = await _firestore.collection('orders').doc(orderId).get();
      final data = doc.data() as Map<String, dynamic>;
      bool isLegacyFormat = data.containsKey('userName') || data.containsKey('userPhone');

      final updateData = <String, dynamic>{
        'status': order_model.OrderStatus.cancelled.name,
        'cancellationReason': reason,
      };

      // Add updatedAt and createdAt for legacy orders if needed
      if (isLegacyFormat) {
        updateData['updatedAt'] = FieldValue.serverTimestamp();
        if (!data.containsKey('createdAt') && data.containsKey('timestamp')) {
          updateData['createdAt'] = data['timestamp'];
        }
      } else {
        updateData['updatedAt'] = FieldValue.serverTimestamp();
      }

      await _firestore
          .collection('orders')
          .doc(orderId)
          .update(updateData);
    } catch (e) {
      if (e is OrderException) rethrow;
      throw OrderException('Failed to cancel order: $e');
    }
  }

  // Add notes to order
  Future<void> addOrderNotes(String orderId, String notes) async {
    try {
      if (!_authService.isAuthenticated) {
        throw OrderException('Authentication required');
      }

      // Check if it's legacy format
      final doc = await _firestore.collection('orders').doc(orderId).get();
      final data = doc.data() as Map<String, dynamic>;
      bool isLegacyFormat = data.containsKey('userName') || data.containsKey('userPhone');

      final updateData = <String, dynamic>{
        'notes': notes,
      };

      // Add updatedAt and createdAt for legacy orders if needed
      if (isLegacyFormat) {
        updateData['updatedAt'] = FieldValue.serverTimestamp();
        if (!data.containsKey('createdAt') && data.containsKey('timestamp')) {
          updateData['createdAt'] = data['timestamp'];
        }
      } else {
        updateData['updatedAt'] = FieldValue.serverTimestamp();
      }

      await _firestore
          .collection('orders')
          .doc(orderId)
          .update(updateData);
    } catch (e) {
      if (e is OrderException) rethrow;
      throw OrderException('Failed to add order notes: $e');
    }
  }

  // Get orders by date range
  Future<List<order_model.Order>> getOrdersByDateRange(DateTime startDate, DateTime endDate) async {
    try {
      final snapshot = await _firestore
          .collection('orders')
          .where('timestamp', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .where('timestamp', isLessThanOrEqualTo: Timestamp.fromDate(endDate))
          .orderBy('timestamp', descending: true)
          .get();

      return snapshot.docs.map((doc) {
        try {
          return order_model.Order.fromFirestore(doc);
        } catch (e) {
          if (kDebugMode) {
            print('Error parsing order ${doc.id}: $e');
          }
          return null;
        }
      }).where((order) => order != null).cast<order_model.Order>().toList();
    } catch (e) {
      throw OrderException('Failed to get orders by date range: $e');
    }
  }

  // Get order statistics
  Future<OrderStatistics> getOrderStatistics() async {
    try {
      final now = DateTime.now();
      final startOfDay = DateTime(now.year, now.month, now.day);
      final startOfWeek = startOfDay.subtract(Duration(days: now.weekday - 1));
      final startOfMonth = DateTime(now.year, now.month, 1);

      // Get today's orders
      final todayOrders = await getOrdersByDateRange(startOfDay, now);
      
      // Get this week's orders
      final weekOrders = await getOrdersByDateRange(startOfWeek, now);
      
      // Get this month's orders
      final monthOrders = await getOrdersByDateRange(startOfMonth, now);

      // Calculate statistics
      final todayRevenue = todayOrders.fold<double>(
        0.0, (total, order) => total + order.totalAmount
      );

      final weekRevenue = weekOrders.fold<double>(
        0.0, (total, order) => total + order.totalAmount
      );

      final monthRevenue = monthOrders.fold<double>(
        0.0, (total, order) => total + order.totalAmount
      );

      // Count orders by status
      final pendingOrders = todayOrders.where((o) => o.status == order_model.OrderStatus.pending).length;
      final processingOrders = todayOrders.where((o) => o.status == order_model.OrderStatus.processing).length;
      final deliveredOrders = todayOrders.where((o) => o.status == order_model.OrderStatus.delivered).length;

      return OrderStatistics(
        todayOrderCount: todayOrders.length,
        weekOrderCount: weekOrders.length,
        monthOrderCount: monthOrders.length,
        todayRevenue: todayRevenue,
        weekRevenue: weekRevenue,
        monthRevenue: monthRevenue,
        pendingOrderCount: pendingOrders,
        processingOrderCount: processingOrders,
        deliveredOrderCount: deliveredOrders,
      );
    } catch (e) {
      throw OrderException('Failed to get order statistics: $e');
    }
  }

  // Search orders
  Future<List<order_model.Order>> searchOrders(String query) async {
    try {
      if (query.trim().isEmpty) return [];

      final queryLower = query.toLowerCase().trim();
      
      // Search by order number
      final orderNumberQuery = await _firestore
          .collection('orders')
          .where('orderNumber', isGreaterThanOrEqualTo: queryLower)
          .where('orderNumber', isLessThanOrEqualTo: '$queryLower\uf8ff')
          .get();

      // Search by customer name
      final customerNameQuery = await _firestore
          .collection('orders')
          .where('customerInfo.name', isGreaterThanOrEqualTo: queryLower)
          .where('customerInfo.name', isLessThanOrEqualTo: '$queryLower\uf8ff')
          .get();

      final orders = <order_model.Order>[];
      final seenIds = <String>{};

      // Add orders from order number search
      for (final doc in orderNumberQuery.docs) {
        if (!seenIds.contains(doc.id)) {
          try {
            orders.add(order_model.Order.fromFirestore(doc));
            seenIds.add(doc.id);
          } catch (e) {
            if (kDebugMode) {
              print('Error parsing order ${doc.id}: $e');
            }
          }
        }
      }

      // Add orders from customer name search
      for (final doc in customerNameQuery.docs) {
        if (!seenIds.contains(doc.id)) {
          try {
            orders.add(order_model.Order.fromFirestore(doc));
            seenIds.add(doc.id);
          } catch (e) {
            if (kDebugMode) {
              print('Error parsing order ${doc.id}: $e');
            }
          }
        }
      }

      return orders;
    } catch (e) {
      throw OrderException('Failed to search orders: $e');
    }
  }

  // Delete order (admin only)
  Future<void> deleteOrder(String orderId) async {
    try {
      if (!_authService.isAuthenticated) {
        throw OrderException('Authentication required');
      }

      await _firestore
          .collection('orders')
          .doc(orderId)
          .delete();
    } catch (e) {
      if (e is OrderException) rethrow;
      throw OrderException('Failed to delete order: $e');
    }
  }

  // Partial cancellation of order items by indices
  Future<void> partialCancelOrderByIndices(
    String orderId,
    List<int> itemIndicesToCancel,
    String reason
  ) async {
    try {
      if (!_authService.isAuthenticated) {
        throw OrderException('Authentication required');
      }

      final order = await getOrder(orderId);
      if (order == null) {
        throw OrderException('Order not found');
      }

      if (!order.canBeCancelled) {
        throw OrderException('Order cannot be cancelled at this stage');
      }

      // Calculate new totals after removing cancelled items by indices
      final remainingItems = <order_model.OrderItem>[];
      final cancelledItems = <order_model.OrderItem>[];

      for (int i = 0; i < order.items.length; i++) {
        if (itemIndicesToCancel.contains(i)) {
          cancelledItems.add(order.items[i]);
        } else {
          remainingItems.add(order.items[i]);
        }
      }

      if (remainingItems.isEmpty) {
        // If all items are cancelled, cancel the entire order
        await cancelOrder(orderId, reason);
        return;
      }

      // Calculate new totals
      final newSubtotal = remainingItems.fold(
        0.0,
        (total, item) => total + (item.price * item.quantity)
      );
      final newTotalAmount = newSubtotal + order.deliveryFee;

      // Update order with remaining items
      final updateData = <String, dynamic>{
        'items': remainingItems.map((item) => item.toMap()).toList(),
        'subtotal': newSubtotal,
        'totalAmount': newTotalAmount,
        'status': order_model.OrderStatus.partialCancelled.name,
        'cancellationReason': reason,
        'cancelledItems': cancelledItems.map((item) => item.toMap()).toList(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      await _firestore
          .collection('orders')
          .doc(orderId)
          .update(updateData);

      // Restore stock for cancelled items
      await _restoreStockForCancelledItems(cancelledItems);

      if (kDebugMode) {
        print('Order $orderId partially cancelled successfully');
      }
    } catch (e) {
      if (e is OrderException) rethrow;
      throw OrderException('Failed to partially cancel order: $e');
    }
  }

  // Partial cancellation of order items (legacy method - kept for compatibility)
  Future<void> partialCancelOrder(
    String orderId,
    List<String> itemsToCancel,
    String reason
  ) async {
    try {
      if (!_authService.isAuthenticated) {
        throw OrderException('Authentication required');
      }

      final order = await getOrder(orderId);
      if (order == null) {
        throw OrderException('Order not found');
      }

      if (!order.canBeCancelled) {
        throw OrderException('Order cannot be cancelled at this stage');
      }

      // Calculate new totals after removing cancelled items
      final remainingItems = order.items.where(
        (item) => !itemsToCancel.contains(item.productId)
      ).toList();

      final cancelledItems = order.items.where(
        (item) => itemsToCancel.contains(item.productId)
      ).toList();

      if (remainingItems.isEmpty) {
        // If all items are cancelled, cancel the entire order
        await cancelOrder(orderId, reason);
        return;
      }

      // Calculate new totals
      final newSubtotal = remainingItems.fold(
        0.0,
        (total, item) => total + (item.price * item.quantity)
      );
      final newTotalAmount = newSubtotal + order.deliveryFee;

      // Update order with remaining items
      final updateData = <String, dynamic>{
        'items': remainingItems.map((item) => item.toMap()).toList(),
        'subtotal': newSubtotal,
        'totalAmount': newTotalAmount,
        'status': order_model.OrderStatus.partialCancelled.name,
        'cancellationReason': reason,
        'cancelledItems': cancelledItems.map((item) => item.toMap()).toList(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      await _firestore
          .collection('orders')
          .doc(orderId)
          .update(updateData);

      // Restore stock for cancelled items
      await _restoreStockForCancelledItems(cancelledItems);

      if (kDebugMode) {
        print('Order $orderId partially cancelled successfully');
      }
    } catch (e) {
      if (e is OrderException) rethrow;
      throw OrderException('Failed to partially cancel order: $e');
    }
  }

  // Restore stock when items are cancelled
  Future<void> _restoreStockForCancelledItems(List<order_model.OrderItem> cancelledItems) async {
    try {
      for (final item in cancelledItems) {
        await _restoreProductStock(item.productId, item.quantity);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error restoring stock for cancelled items: $e');
      }
      // Don't throw here as the order cancellation should still succeed
    }
  }

  // Restore stock for a specific product
  Future<void> _restoreProductStock(String productId, int quantity) async {
    try {
      final productDoc = await _firestore.collection('products').doc(productId).get();
      if (!productDoc.exists) {
        if (kDebugMode) {
          print('Product $productId not found for stock restoration');
        }
        return;
      }

      final currentStock = productDoc.data()?['stockQuantity'] ?? 0;
      final newStock = currentStock + quantity;

      await _firestore.collection('products').doc(productId).update({
        'stockQuantity': newStock,
        'isAvailable': newStock > 0,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      if (kDebugMode) {
        print('Restored $quantity units to product $productId. New stock: $newStock');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error restoring stock for product $productId: $e');
      }
    }
  }

  // Reduce stock when order is confirmed
  Future<void> reduceStockForOrder(String orderId) async {
    try {
      if (!_authService.isAuthenticated) {
        throw OrderException('Authentication required');
      }

      final order = await getOrder(orderId);
      if (order == null) {
        throw OrderException('Order not found');
      }

      for (final item in order.items) {
        await _reduceProductStock(item.productId, item.quantity);
      }

      if (kDebugMode) {
        print('Stock reduced for order $orderId');
      }
    } catch (e) {
      if (e is OrderException) rethrow;
      throw OrderException('Failed to reduce stock for order: $e');
    }
  }

  // Reduce stock for a specific product
  Future<void> _reduceProductStock(String productId, int quantity) async {
    try {
      final productDoc = await _firestore.collection('products').doc(productId).get();
      if (!productDoc.exists) {
        throw OrderException('Product $productId not found');
      }

      final currentStock = productDoc.data()?['stockQuantity'] ?? 0;
      if (currentStock < quantity) {
        throw OrderException('Insufficient stock for product $productId');
      }

      final newStock = currentStock - quantity;

      await _firestore.collection('products').doc(productId).update({
        'stockQuantity': newStock,
        'isAvailable': newStock > 0,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      if (kDebugMode) {
        print('Reduced $quantity units from product $productId. New stock: $newStock');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error reducing stock for product $productId: $e');
      }
      rethrow;
    }
  }

  // Enhanced update order status with automatic stock management
  Future<void> updateOrderStatusWithStockManagement(
    String orderId,
    order_model.OrderStatus newStatus
  ) async {
    try {
      final order = await getOrder(orderId);
      if (order == null) {
        throw OrderException('Order not found');
      }

      final oldStatus = order.status;

      // Update order status first
      await updateOrderStatus(orderId, newStatus);

      // Handle stock changes based on status transition
      if (oldStatus == order_model.OrderStatus.pending &&
          newStatus == order_model.OrderStatus.confirmed) {
        // Reduce stock when order is confirmed
        await reduceStockForOrder(orderId);
      } else if ((oldStatus == order_model.OrderStatus.confirmed ||
                  oldStatus == order_model.OrderStatus.processing) &&
                 newStatus == order_model.OrderStatus.cancelled) {
        // Restore stock when confirmed/processing order is cancelled
        await _restoreStockForCancelledItems(order.items);
      }

      if (kDebugMode) {
        print('Order $orderId status updated with stock management: $oldStatus -> $newStatus');
      }
    } catch (e) {
      if (e is OrderException) rethrow;
      throw OrderException('Failed to update order status with stock management: $e');
    }
  }

  // Assign delivery person to order
  Future<void> assignDeliveryPerson(String orderId, String deliveryPersonId, String deliveryPersonName) async {
    try {
      if (!_authService.isAuthenticated) {
        throw OrderException('Authentication required');
      }

      final order = await getOrder(orderId);
      if (order == null) {
        throw OrderException('Order not found');
      }

      if (!order.canBeAssignedForDelivery) {
        throw OrderException('Order cannot be assigned for delivery at this stage');
      }

      await _firestore
          .collection('orders')
          .doc(orderId)
          .update({
            'assignedDeliveryPersonId': deliveryPersonId,
            'assignedDeliveryPersonName': deliveryPersonName,
            'status': order_model.OrderStatus.outForDelivery.name,
            'updatedAt': FieldValue.serverTimestamp(),
          });

      if (kDebugMode) {
        print('Order $orderId assigned to delivery person $deliveryPersonId');
      }
    } catch (e) {
      if (e is OrderException) rethrow;
      throw OrderException('Failed to assign delivery person: $e');
    }
  }

  // Unassign delivery person from order
  Future<void> unassignDeliveryPerson(String orderId) async {
    try {
      if (!_authService.isAuthenticated) {
        throw OrderException('Authentication required');
      }

      final order = await getOrder(orderId);
      if (order == null) {
        throw OrderException('Order not found');
      }

      // Only allow unassigning if order is still out for delivery
      if (order.status != order_model.OrderStatus.outForDelivery) {
        throw OrderException('Cannot unassign delivery person from this order');
      }

      await _firestore
          .collection('orders')
          .doc(orderId)
          .update({
            'assignedDeliveryPersonId': FieldValue.delete(),
            'assignedDeliveryPersonName': FieldValue.delete(),
            'status': order_model.OrderStatus.processing.name,
            'updatedAt': FieldValue.serverTimestamp(),
          });

      if (kDebugMode) {
        print('Delivery person unassigned from order $orderId');
      }
    } catch (e) {
      if (e is OrderException) rethrow;
      throw OrderException('Failed to unassign delivery person: $e');
    }
  }

  // Get orders ready for delivery assignment
  Stream<List<order_model.Order>> getOrdersReadyForDelivery() {
    return _firestore
        .collection('orders')
        .where('status', whereIn: [
          order_model.OrderStatus.confirmed.name,
          order_model.OrderStatus.processing.name
        ])
        .orderBy('createdAt', descending: false)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => order_model.Order.fromFirestore(doc))
            .toList());
  }

  // Get orders assigned to a specific delivery person
  Stream<List<order_model.Order>> getOrdersAssignedToDeliveryPerson(String deliveryPersonId) {
    return _firestore
        .collection('orders')
        .where('assignedDeliveryPersonId', isEqualTo: deliveryPersonId)
        .where('status', isEqualTo: order_model.OrderStatus.outForDelivery.name)
        .orderBy('createdAt', descending: false)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => order_model.Order.fromFirestore(doc))
            .toList());
  }
}

// Order statistics model
class OrderStatistics {
  final int todayOrderCount;
  final int weekOrderCount;
  final int monthOrderCount;
  final double todayRevenue;
  final double weekRevenue;
  final double monthRevenue;
  final int pendingOrderCount;
  final int processingOrderCount;
  final int deliveredOrderCount;

  OrderStatistics({
    required this.todayOrderCount,
    required this.weekOrderCount,
    required this.monthOrderCount,
    required this.todayRevenue,
    required this.weekRevenue,
    required this.monthRevenue,
    required this.pendingOrderCount,
    required this.processingOrderCount,
    required this.deliveredOrderCount,
  });

  String get formattedTodayRevenue => '₹${todayRevenue.toStringAsFixed(2)}';
  String get formattedWeekRevenue => '₹${weekRevenue.toStringAsFixed(2)}';
  String get formattedMonthRevenue => '₹${monthRevenue.toStringAsFixed(2)}';
}

// Custom exception class
class OrderException implements Exception {
  final String message;
  
  OrderException(this.message);
  
  @override
  String toString() => message;
}
