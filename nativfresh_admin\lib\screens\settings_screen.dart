import 'package:flutter/material.dart';
import '../services/settings_service.dart';
import 'notification_settings_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final SettingsService _settingsService = SettingsService();
  
  Map<String, dynamic>? _currentSettings;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final settings = await _settingsService.getUpiSettings();
      if (mounted) {
        setState(() {
          _currentSettings = settings;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showUpiSettingsDialog() {
    final upiIdController = TextEditingController(
      text: _currentSettings?['upiId'] ?? '',
    );
    final currentPasswordController = TextEditingController();
    final newPasswordController = TextEditingController();
    final confirmPasswordController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    // Check if this is first-time setup
    final bool isFirstTimeSetup = _currentSettings?['upiId'] == null ||
                                  _currentSettings?['upiPassword'] == null;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isFirstTimeSetup ? 'Setup UPI Payment' : 'Update UPI Settings'),
        content: Form(
          key: formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: upiIdController,
                  decoration: const InputDecoration(
                    labelText: 'UPI ID',
                    hintText: 'example@upi',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.account_balance),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter UPI ID';
                    }
                    if (!value.contains('@')) {
                      return 'Please enter a valid UPI ID';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                // Only show current password field if not first-time setup
                if (!isFirstTimeSetup) ...[
                  TextFormField(
                    controller: currentPasswordController,
                    decoration: const InputDecoration(
                      labelText: 'Current Password',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.lock),
                    ),
                    obscureText: true,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter current password';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                ],
                TextFormField(
                  controller: newPasswordController,
                  decoration: InputDecoration(
                    labelText: isFirstTimeSetup ? 'Password' : 'New Password',
                    hintText: isFirstTimeSetup ? 'Create a secure password' : 'Enter new password',
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.lock_outline),
                  ),
                  obscureText: true,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter new password';
                    }
                    if (value.length < 6) {
                      return 'Password must be at least 6 characters';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: confirmPasswordController,
                  decoration: const InputDecoration(
                    labelText: 'Confirm New Password',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.lock_outline),
                  ),
                  obscureText: true,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please confirm password';
                    }
                    if (value != newPasswordController.text) {
                      return 'Passwords do not match';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isFirstTimeSetup ? Colors.blue.shade50 : Colors.amber.shade50,
                    border: Border.all(color: isFirstTimeSetup ? Colors.blue.shade200 : Colors.amber.shade200),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        isFirstTimeSetup ? Icons.info : Icons.warning,
                        color: isFirstTimeSetup ? Colors.blue.shade700 : Colors.amber.shade700
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          isFirstTimeSetup
                              ? 'Set up your UPI ID and password for secure payment collection by delivery persons.'
                              : 'This UPI ID will be used for payment collection by delivery persons.',
                          style: TextStyle(
                            color: isFirstTimeSetup ? Colors.blue.shade700 : Colors.amber.shade700,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (formKey.currentState!.validate()) {
                try {
                  await _settingsService.updateUpiSettings(
                    upiId: upiIdController.text.trim(),
                    password: newPasswordController.text,
                    currentPassword: isFirstTimeSetup ? null : currentPasswordController.text,
                  );
                  
                  if (mounted) {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(isFirstTimeSetup
                            ? 'UPI payment setup completed successfully'
                            : 'UPI settings updated successfully'),
                        backgroundColor: Colors.green,
                      ),
                    );
                    _loadSettings(); // Reload settings
                  }
                } catch (e) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Error: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              }
            },
            child: Text(isFirstTimeSetup ? 'Setup' : 'Update'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: Colors.green.shade600,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Payment Settings',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // UPI Settings Card
                  Card(
                    child: ListTile(
                      leading: const Icon(Icons.qr_code, color: Colors.blue),
                      title: const Text('UPI Payment Configuration'),
                      subtitle: Text(
                        _currentSettings?['upiId'] != null
                            ? 'UPI ID: ${_currentSettings!['upiId']}'
                            : 'UPI not configured',
                      ),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: _showUpiSettingsDialog,
                    ),
                  ),
                  const SizedBox(height: 8),
                  
                  // Info Card
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      border: Border.all(color: Colors.blue.shade200),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.info, color: Colors.blue.shade700),
                            const SizedBox(width: 8),
                            Text(
                              'UPI Payment Information',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue.shade700,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '• Configure your UPI ID for payment collection\n'
                          '• Delivery persons will show QR codes to customers\n'
                          '• Password protects UPI settings from unauthorized changes\n'
                          '• Customers can scan QR code to pay directly to your UPI ID',
                          style: TextStyle(color: Colors.blue.shade600),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Additional Settings Section
                  Text(
                    'App Settings',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  Card(
                    child: Column(
                      children: [
                        ListTile(
                          leading: const Icon(Icons.delivery_dining, color: Colors.green),
                          title: const Text('Delivery Management'),
                          subtitle: const Text('Manage delivery persons and assignments'),
                          trailing: const Icon(Icons.arrow_forward_ios),
                          onTap: () {
                            // Navigate to delivery persons screen
                            Navigator.pushNamed(context, '/delivery-persons');
                          },
                        ),
                        const Divider(height: 1),
                        ListTile(
                          leading: const Icon(Icons.notifications, color: Colors.orange),
                          title: const Text('Notifications'),
                          subtitle: const Text('Configure app notifications'),
                          trailing: const Icon(Icons.arrow_forward_ios),
                          onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const NotificationSettingsScreen(),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
    );
  }
}
