import 'dart:io';
import 'dart:convert';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:image/image.dart' as img;
import 'package:file_picker/file_picker.dart';

class StorageService {
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  final FirebaseStorage _storage = FirebaseStorage.instance;

  // Pick image from device
  Future<File?> pickImage() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        return File(result.files.first.path!);
      }
      return null;
    } catch (e) {
      throw StorageException('Failed to pick image: $e');
    }
  }

  // Upload product image
  Future<String> uploadProductImage(File imageFile) async {
    try {
      // Validate file
      _validateImageFile(imageFile);
      
      // Process image (resize and compress)
      final processedImage = await _processImage(imageFile);
      
      // Generate unique filename
      final fileName = _generateFileName(imageFile.path);
      final ref = _storage.ref().child('products/$fileName');
      
      // Upload file
      final uploadTask = ref.putData(
        processedImage,
        SettableMetadata(
          contentType: 'image/jpeg',
          customMetadata: {
            'uploadedAt': DateTime.now().toIso8601String(),
            'originalName': path.basename(imageFile.path),
          },
        ),
      );
      
      // Wait for upload to complete
      final snapshot = await uploadTask;
      
      // Get download URL
      final downloadUrl = await snapshot.ref.getDownloadURL();
      
      return downloadUrl;
    } catch (e) {
      throw StorageException('Failed to upload image: $e');
    }
  }

  // Upload multiple images
  Future<List<String>> uploadMultipleImages(List<File> imageFiles) async {
    try {
      final urls = <String>[];
      
      for (final file in imageFiles) {
        final url = await uploadProductImage(file);
        urls.add(url);
      }
      
      return urls;
    } catch (e) {
      throw StorageException('Failed to upload multiple images: $e');
    }
  }

  // Delete image
  Future<void> deleteImage(String imageUrl) async {
    try {
      // Extract path from URL
      final ref = _storage.refFromURL(imageUrl);
      await ref.delete();
    } catch (e) {
      // Don't throw error if image doesn't exist
      if (kDebugMode) {
        print('Warning: Could not delete image $imageUrl: $e');
      }
    }
  }

  // Delete multiple images
  Future<void> deleteMultipleImages(List<String> imageUrls) async {
    for (final url in imageUrls) {
      await deleteImage(url);
    }
  }

  // Get image metadata
  Future<FullMetadata?> getImageMetadata(String imageUrl) async {
    try {
      final ref = _storage.refFromURL(imageUrl);
      return await ref.getMetadata();
    } catch (e) {
      if (kDebugMode) {
        print('Failed to get image metadata: $e');
      }
      return null;
    }
  }

  // Private methods
  void _validateImageFile(File file) {
    // Check if file exists
    if (!file.existsSync()) {
      throw StorageException('Image file does not exist');
    }
    
    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    final fileSize = file.lengthSync();
    if (fileSize > maxSize) {
      throw StorageException('Image file is too large. Maximum size is 10MB.');
    }
    
    // Check file extension
    final extension = path.extension(file.path).toLowerCase();
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp'];
    if (!allowedExtensions.contains(extension)) {
      throw StorageException(
        'Invalid image format. Allowed formats: ${allowedExtensions.join(', ')}'
      );
    }
  }

  Future<Uint8List> _processImage(File imageFile) async {
    try {
      // Read image
      final bytes = await imageFile.readAsBytes();
      final image = img.decodeImage(bytes);
      
      if (image == null) {
        throw StorageException('Invalid image file');
      }
      
      // Resize image if too large
      img.Image resizedImage = image;
      const maxWidth = 1200;
      const maxHeight = 1200;
      
      if (image.width > maxWidth || image.height > maxHeight) {
        resizedImage = img.copyResize(
          image,
          width: image.width > image.height ? maxWidth : null,
          height: image.height > image.width ? maxHeight : null,
        );
      }
      
      // Compress image
      final compressedBytes = img.encodeJpg(resizedImage, quality: 85);
      
      return Uint8List.fromList(compressedBytes);
    } catch (e) {
      throw StorageException('Failed to process image: $e');
    }
  }

  String _generateFileName(String originalPath) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = DateTime.now().microsecond;
    return '${timestamp}_$random.jpg'; // Always use .jpg for processed images
  }
}

// Local storage service for device storage
class LocalStorageService {
  static final LocalStorageService _instance = LocalStorageService._internal();
  factory LocalStorageService() => _instance;
  LocalStorageService._internal();

  // Save data to local storage
  Future<void> saveData(String key, String data) async {
    try {
      // For web, we'll use localStorage through shared_preferences
      // For mobile, this will use the device's local storage
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(key, data);
    } catch (e) {
      throw StorageException('Failed to save data locally: $e');
    }
  }

  // Get data from local storage
  Future<String?> getData(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(key);
    } catch (e) {
      if (kDebugMode) {
        print('Failed to get local data: $e');
      }
      return null;
    }
  }

  // Remove data from local storage
  Future<void> removeData(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(key);
    } catch (e) {
      if (kDebugMode) {
        print('Failed to remove local data: $e');
      }
    }
  }

  // Clear all local data
  Future<void> clearAll() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
    } catch (e) {
      if (kDebugMode) {
        print('Failed to clear local data: $e');
      }
    }
  }

  // Save JSON data
  Future<void> saveJsonData(String key, Map<String, dynamic> data) async {
    try {
      final jsonString = jsonEncode(data);
      await saveData(key, jsonString);
    } catch (e) {
      throw StorageException('Failed to save JSON data: $e');
    }
  }

  // Get JSON data
  Future<Map<String, dynamic>?> getJsonData(String key) async {
    try {
      final jsonString = await getData(key);
      if (jsonString == null) return null;
      
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      if (kDebugMode) {
        print('Failed to get JSON data: $e');
      }
      return null;
    }
  }

  // Cache management
  Future<void> cacheProductData(List<Map<String, dynamic>> products) async {
    await saveJsonData('cached_products', {
      'products': products,
      'cachedAt': DateTime.now().toIso8601String(),
    });
  }

  Future<List<Map<String, dynamic>>?> getCachedProductData() async {
    try {
      final data = await getJsonData('cached_products');
      if (data == null) return null;
      
      // Check if cache is still valid (24 hours)
      final cachedAt = DateTime.parse(data['cachedAt']);
      final now = DateTime.now();
      final difference = now.difference(cachedAt);
      
      if (difference.inHours > 24) {
        await removeData('cached_products');
        return null;
      }
      
      return (data['products'] as List).cast<Map<String, dynamic>>();
    } catch (e) {
      if (kDebugMode) {
        print('Failed to get cached product data: $e');
      }
      return null;
    }
  }

  // App settings
  Future<void> saveAppSettings(Map<String, dynamic> settings) async {
    await saveJsonData('app_settings', settings);
  }

  Future<Map<String, dynamic>?> getAppSettings() async {
    return await getJsonData('app_settings');
  }

  // User preferences
  Future<void> saveUserPreferences(Map<String, dynamic> preferences) async {
    await saveJsonData('user_preferences', preferences);
  }

  Future<Map<String, dynamic>?> getUserPreferences() async {
    return await getJsonData('user_preferences');
  }
}

// Custom exception class
class StorageException implements Exception {
  final String message;
  
  StorageException(this.message);
  
  @override
  String toString() => message;
}


