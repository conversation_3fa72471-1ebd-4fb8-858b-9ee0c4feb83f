import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../config/environment.dart';

/// Utility class to test Firebase configuration and connectivity
class FirebaseTest {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Test Firebase configuration and connectivity
  static Future<Map<String, dynamic>> testConfiguration() async {
    final results = <String, dynamic>{};
    
    try {
      // Test 1: Check Firebase initialization
      results['firebase_initialized'] = _auth.app.name == '[DEFAULT]';
      results['project_id'] = _auth.app.options.projectId;
      results['auth_domain'] = _auth.app.options.authDomain;
      
      // Test 2: Check environment configuration
      results['environment_valid'] = Environment.isValidConfiguration;
      results['environment_name'] = Environment.environmentName;
      
      // Test 3: Test Firestore connectivity
      try {
        await _firestore.collection('test').limit(1).get();
        results['firestore_connected'] = true;
      } catch (e) {
        results['firestore_connected'] = false;
        results['firestore_error'] = e.toString();
      }
      
      // Test 4: Test Auth service availability
      try {
        final currentUser = _auth.currentUser;
        results['auth_service_available'] = true;
        results['current_user'] = currentUser?.uid ?? 'none';
      } catch (e) {
        results['auth_service_available'] = false;
        results['auth_error'] = e.toString();
      }
      
      // Test 5: Platform-specific checks
      if (kIsWeb) {
        results['platform'] = 'web';
        results['web_app_id'] = _auth.app.options.appId;
        
        // Check if running on localhost
        results['is_localhost'] = Uri.base.host == 'localhost';
        results['current_host'] = Uri.base.host;
        results['current_port'] = Uri.base.port;
      } else {
        results['platform'] = 'mobile';
      }
      
      results['test_completed'] = true;
      results['test_timestamp'] = DateTime.now().toIso8601String();
      
    } catch (e) {
      results['test_completed'] = false;
      results['test_error'] = e.toString();
    }
    
    return results;
  }

  /// Print test results to console (debug mode only)
  static Future<void> printTestResults() async {
    if (!kDebugMode) return;
    
    print('=== Firebase Configuration Test ===');
    final results = await testConfiguration();
    
    results.forEach((key, value) {
      print('$key: $value');
    });
    print('===================================');
  }

  /// Test a specific delivery person login (for debugging)
  static Future<Map<String, dynamic>> testDeliveryLogin(String username) async {
    final results = <String, dynamic>{};
    
    try {
      final firebaseEmail = '${username.trim().toLowerCase()}@nativfresh.com';
      results['firebase_email'] = firebaseEmail;
      
      // Check if user exists in Firebase Auth (without password)
      try {
        final methods = await _auth.fetchSignInMethodsForEmail(firebaseEmail);
        results['user_exists_in_auth'] = methods.isNotEmpty;
        results['sign_in_methods'] = methods;
      } catch (e) {
        results['user_exists_in_auth'] = false;
        results['auth_check_error'] = e.toString();
      }
      
      // Check if delivery person document exists
      try {
        final query = await _firestore
            .collection('delivery_persons')
            .where('username', isEqualTo: username.trim().toLowerCase())
            .limit(1)
            .get();
            
        results['delivery_doc_exists'] = query.docs.isNotEmpty;
        if (query.docs.isNotEmpty) {
          final doc = query.docs.first;
          results['delivery_doc_id'] = doc.id;
          results['delivery_data'] = doc.data();
        }
      } catch (e) {
        results['delivery_doc_exists'] = false;
        results['firestore_check_error'] = e.toString();
      }
      
    } catch (e) {
      results['test_error'] = e.toString();
    }
    
    return results;
  }
}
