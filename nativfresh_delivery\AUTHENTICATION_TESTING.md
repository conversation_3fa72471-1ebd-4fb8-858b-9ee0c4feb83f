# Delivery App Authentication Testing Guide

## Overview
The delivery app uses a secure authentication system where:
- **<PERSON><PERSON> creates accounts** in Firebase Auth and Firestore
- **Delivery personnel only login** with provided credentials
- **No self-registration** is allowed for delivery personnel

## Authentication Flow
1. <PERSON><PERSON> creates delivery person account in Firebase Auth with email format: `<EMAIL>`
2. <PERSON><PERSON> creates corresponding document in Firestore `delivery_persons` collection
3. Delivery person logs in using their username (not email) and password
4. App converts username to email format and authenticates with Firebase
5. App verifies delivery person document exists and is active

## Testing on Different Platforms

### Web Platform Testing
1. **Run the app in Chrome:**
   ```bash
   cd nativfresh_delivery
   flutter run -d chrome --web-port=8080
   ```

2. **Check browser console** for debug information
3. **Use Debug Screen** (available in debug mode) to check:
   - Firebase configuration
   - Network connectivity
   - Authentication service status

### Android Platform Testing
1. **Run the app on Android device/emulator:**
   ```bash
   cd nativfresh_delivery
   flutter run -d android
   ```

2. **Check Android logs:**
   ```bash
   flutter logs
   ```

## Debug Features (Debug Mode Only)

### Debug Screen Access
- Available only in debug mode
- Access via "Debug Info" button on login screen
- Shows comprehensive authentication status

### Debug Information Includes:
- Platform detection (Web/Mobile)
- Firebase configuration status
- Firestore connectivity
- Current authentication state
- User existence verification

## Common Issues and Solutions

### Web Platform Issues

#### 1. CORS/Domain Authorization
**Symptoms:** Authentication fails with network errors
**Solution:** Ensure localhost is authorized in Firebase Console:
- Go to Firebase Console → Authentication → Settings
- Add `localhost` to authorized domains

#### 2. Web App Configuration
**Symptoms:** Invalid app ID or configuration errors
**Solution:** Verify web app configuration:
- Check `firebase_options.dart` has correct web app ID
- Ensure `authDomain` is correct: `nativfresh-e5b0f.firebaseapp.com`

#### 3. Browser Storage Issues
**Symptoms:** Login state not persisting
**Solution:** 
- Clear browser cache and cookies
- Check if third-party cookies are enabled
- Ensure browser supports local storage

### Android Platform Issues

#### 1. Network Security
**Symptoms:** Network requests failing
**Solution:** Check network security configuration in `android/app/src/main/AndroidManifest.xml`

#### 2. Google Services Configuration
**Symptoms:** Firebase initialization fails
**Solution:** Verify `google-services.json` is correctly placed and contains delivery app configuration

### General Issues

#### 1. User Not Found
**Symptoms:** "Invalid username or password" error
**Possible Causes:**
- User doesn't exist in Firebase Auth
- Username format incorrect
- Delivery person document missing in Firestore

**Debug Steps:**
1. Use Debug Screen to test specific username
2. Check if user exists in Firebase Auth
3. Verify delivery person document exists and is active

#### 2. Account Disabled
**Symptoms:** "Your account has been disabled" error
**Solution:** Admin needs to enable the account in Firebase Console

#### 3. Network Connectivity
**Symptoms:** "Network error" messages
**Solution:**
- Check internet connection
- Verify Firebase project is accessible
- Check firewall/proxy settings

## Testing Checklist

### Before Testing
- [ ] Firebase project configured correctly
- [ ] Delivery person account created by admin
- [ ] Account is active in Firestore
- [ ] Correct Firebase configuration in app

### Web Testing
- [ ] App loads without errors
- [ ] Debug screen shows correct configuration
- [ ] Login with valid credentials works
- [ ] Login with invalid credentials shows appropriate error
- [ ] Authentication state persists on page refresh

### Android Testing
- [ ] App installs and launches
- [ ] Firebase services initialize correctly
- [ ] Login functionality works
- [ ] Authentication state persists across app restarts
- [ ] Network connectivity is stable

### Cross-Platform Testing
- [ ] Same credentials work on both platforms
- [ ] Error messages are consistent
- [ ] Authentication state syncs properly
- [ ] Performance is acceptable on both platforms

## Troubleshooting Commands

### Flutter Commands
```bash
# Clean and rebuild
flutter clean
flutter pub get
flutter run

# Check for issues
flutter doctor
flutter analyze

# View logs
flutter logs
```

### Firebase Commands
```bash
# Check Firebase configuration
firebase projects:list
firebase use nativfresh-e5b0f
firebase auth:export users.json
```

## Support Information

If authentication issues persist:
1. Check debug screen output
2. Review browser/device console logs
3. Verify Firebase Console settings
4. Contact admin for account verification

## Security Notes

- Usernames are converted to email format: `<EMAIL>`
- Only admin can create delivery accounts
- Accounts can be disabled by admin
- Authentication state is stored securely
- Session timeout is configurable (default: 60 minutes)
