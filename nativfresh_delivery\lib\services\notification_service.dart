import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'auth_service.dart';
import '../config/notification_config.dart';

@Deprecated('Use SimpleNotificationService instead')
class LegacyNotificationService {
  static final LegacyNotificationService _instance = LegacyNotificationService._internal();
  factory LegacyNotificationService() => _instance;
  LegacyNotificationService._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AuthService _authService = AuthService();

  bool _isInitialized = false;
  String? _fcmToken;

  // Initialize notification service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Request notification permissions
      await _requestPermissions();

      // Initialize local notifications
      await _initializeLocalNotifications();

      // Initialize Firebase messaging
      await _initializeFirebaseMessaging();

      // Get and save FCM token
      await _getFCMToken();

      _isInitialized = true;
    } catch (e) {
      // Log error in production-appropriate way
      debugPrint('Error initializing NotificationService: $e');
    }
  }

  // Request notification permissions
  Future<void> _requestPermissions() async {
    // Request notification permission
    await Permission.notification.request();

    // Request Firebase messaging permission
    await _firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: NotificationConfig.enableBadge,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: NotificationConfig.enableSound,
    );
  }

  // Initialize local notifications
  Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );
  }

  // Initialize Firebase messaging
  Future<void> _initializeFirebaseMessaging() async {
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);

    // Handle notification taps when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

    // Handle notification tap when app is terminated
    final initialMessage = await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      _handleNotificationTap(initialMessage);
    }
  }

  // Get FCM token and save to Firestore
  Future<void> _getFCMToken() async {
    try {
      _fcmToken = await _firebaseMessaging.getToken();
      if (_fcmToken != null) {
        await _saveFCMTokenToFirestore(_fcmToken!);
      }

      // Listen for token refresh
      _firebaseMessaging.onTokenRefresh.listen((newToken) {
        _fcmToken = newToken;
        _saveFCMTokenToFirestore(newToken);
      });
    } catch (e) {
      debugPrint('Error getting FCM token: $e');
    }
  }

  // Save FCM token to Firestore for the current delivery person
  Future<void> _saveFCMTokenToFirestore(String token) async {
    try {
      final deliveryPersonInfo = await _authService.getSavedDeliveryPersonInfo();
      if (deliveryPersonInfo != null) {
        await _firestore
            .collection('delivery_persons')
            .doc(deliveryPersonInfo['id'])
            .update({
          'fcmToken': token,
          'tokenUpdatedAt': FieldValue.serverTimestamp(),
        });
      }
    } catch (e) {
      debugPrint('Error saving FCM token: $e');
    }
  }

  // Handle foreground messages
  void _handleForegroundMessage(RemoteMessage message) {
    // Show local notification for foreground messages
    _showLocalNotification(
      title: message.notification?.title ?? 'New Notification',
      body: message.notification?.body ?? '',
      data: message.data,
    );
  }

  // Handle notification tap
  void _handleNotificationTap(RemoteMessage message) {
    // Handle navigation based on notification data
    _handleNotificationNavigation(message.data);
  }

  // Handle local notification tap
  void _onNotificationTapped(NotificationResponse response) {
    // Parse payload and handle navigation
    if (response.payload != null) {
      // Parse JSON payload here if needed
      _handleNotificationNavigation({});
    }
  }

  // Handle notification navigation
  void _handleNotificationNavigation(Map<String, dynamic> data) {
    // Handle navigation based on notification type
    final notificationType = data['type'] as String?;

    switch (notificationType) {
      case 'new_order':
      case 'order_update':
        // Navigation will be handled by the app's routing system
        break;
      case 'payment_reminder':
        // Handle payment-related navigation
        break;
      default:
        // Default handling
        break;
    }
  }

  // Show local notification
  Future<void> _showLocalNotification({
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    const androidDetails = AndroidNotificationDetails(
      NotificationConfig.channelId,
      NotificationConfig.channelName,
      channelDescription: NotificationConfig.channelDescription,
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
      enableVibration: NotificationConfig.enableVibration,
      playSound: NotificationConfig.enableSound,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: NotificationConfig.enableBadge,
      presentSound: NotificationConfig.enableSound,
    );

    const notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      DateTime.now().millisecondsSinceEpoch.remainder(100000),
      title,
      body,
      notificationDetails,
      payload: data?.toString(),
    );
  }

  // Show custom popup notification
  static void showPopupNotification(
    BuildContext context, {
    required String title,
    required String message,
    IconData? icon,
    Color? backgroundColor,
    Duration duration = const Duration(seconds: 4),
  }) {
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).padding.top + 10,
        left: 16,
        right: 16,
        child: Material(
          color: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: backgroundColor ?? Colors.green.shade600,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                if (icon != null) ...[
                  Icon(icon, color: Colors.white, size: 24),
                  const SizedBox(width: 12),
                ],
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        message,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => overlayEntry.remove(),
                  icon: const Icon(Icons.close, color: Colors.white),
                ),
              ],
            ),
          ),
        ),
      ),
    );

    overlay.insert(overlayEntry);

    // Auto remove after duration
    Future.delayed(duration, () {
      if (overlayEntry.mounted) {
        overlayEntry.remove();
      }
    });
  }

  // Send notification to specific delivery person (for admin use)
  Future<void> sendNotificationToDeliveryPerson({
    required String deliveryPersonId,
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    try {
      // Get delivery person's FCM token
      final deliveryPersonDoc = await _firestore
          .collection('delivery_persons')
          .doc(deliveryPersonId)
          .get();

      if (deliveryPersonDoc.exists) {
        final fcmToken = deliveryPersonDoc.data()?['fcmToken'];
        if (fcmToken != null) {
          // Here you would typically use a cloud function or admin SDK
          // to send the notification. For now, we'll store it in Firestore
          // and let the client handle it.
          await _firestore.collection('notifications').add({
            'targetDeliveryPersonId': deliveryPersonId,
            'title': title,
            'body': body,
            'data': data ?? {},
            'createdAt': FieldValue.serverTimestamp(),
            'read': false,
          });
        }
      }
    } catch (e) {
      debugPrint('Error sending notification: $e');
    }
  }

  // Get FCM token
  String? get fcmToken => _fcmToken;

  // Check if initialized
  bool get isInitialized => _isInitialized;
}

// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> _handleBackgroundMessage(RemoteMessage message) async {
  // Handle background message processing
  // This runs when the app is in background or terminated
}
