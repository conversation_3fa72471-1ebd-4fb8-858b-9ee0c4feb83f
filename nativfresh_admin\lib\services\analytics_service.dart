import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/order.dart' as order_model;
import '../services/auth_service.dart';

/// Production-grade analytics service for comprehensive business metrics
class AnalyticsService {
  static final AnalyticsService _instance = AnalyticsService._internal();
  factory AnalyticsService() => _instance;
  AnalyticsService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AuthService _authService = AuthService();

  /// Get comprehensive dashboard analytics
  /// Note: For delivered orders, metrics are based on delivery date for accurate delivery tracking
  /// For other orders, metrics are based on creation date
  Future<DashboardAnalytics> getDashboardAnalytics() async {
    try {
      if (!_authService.isAuthenticated) {
        throw AnalyticsException('Authentication required');
      }

      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);

      final weekStart = today.subtract(Duration(days: now.weekday - 1));
      final monthStart = DateTime(now.year, now.month, 1);
      final lastMonthStart = DateTime(now.year, now.month - 1, 1);
      final lastMonthEnd = DateTime(now.year, now.month, 0);

      // Fetch all orders for analysis
      // Try to get orders ordered by timestamp first (for legacy orders),
      // then fall back to createdAt if that fails
      QuerySnapshot ordersSnapshot;
      try {
        ordersSnapshot = await _firestore
            .collection('orders')
            .orderBy('timestamp', descending: true)
            .get();
      } catch (e) {
        // Fallback to createdAt if timestamp field doesn't exist
        ordersSnapshot = await _firestore
            .collection('orders')
            .orderBy('createdAt', descending: true)
            .get();
      }

      final orders = ordersSnapshot.docs
          .map((doc) {
            try {
              return order_model.Order.fromFirestore(doc);
            } catch (e) {
              if (kDebugMode) {
                print('Error parsing order ${doc.id}: $e');
                print('Order data: ${doc.data()}');
              }
              return null;
            }
          })
          .where((order) => order != null)
          .cast<order_model.Order>()
          .toList();

      // Debug logging
      if (kDebugMode) {
        print('Analytics Debug: Total orders found: ${orders.length}');
        print('Analytics Debug: Date range - Today: $today to $now');
        for (int i = 0; i < orders.length && i < 5; i++) {
          print('Analytics Debug: Order ${i + 1}: ${orders[i].id}, Date: ${orders[i].createdAt}, Status: ${orders[i].status}');
        }
      }

      // Calculate metrics
      final todayOrders = _filterOrdersByDate(orders, today, now);

      final weekOrders = _filterOrdersByDate(orders, weekStart, now);
      final monthOrders = _filterOrdersByDate(orders, monthStart, now);
      final lastMonthOrders = _filterOrdersByDate(orders, lastMonthStart, lastMonthEnd);

      // Debug logging for filtered results
      if (kDebugMode) {
        print('Analytics Debug: Today orders: ${todayOrders.length}');
        print('Analytics Debug: Week orders: ${weekOrders.length}');
        print('Analytics Debug: Month orders: ${monthOrders.length}');
      }

      // Revenue calculations (will be recalculated for completed orders below)

      // Status counts
      final statusCounts = _calculateStatusCounts(orders);
      final todayStatusCounts = _calculateStatusCounts(todayOrders);

      // Debug logging for status counts
      if (kDebugMode) {
        print('Analytics Debug: Status counts: $statusCounts');
        print('Analytics Debug: Today status counts: $todayStatusCounts');
      }

      // Growth calculations (will be recalculated for completed orders below)

      // Top products
      final topProducts = await _getTopProducts(monthStart, now);

      // Calculate completed orders (delivered orders only for business metrics)
      final completedOrders = orders.where((order) => order.status == order_model.OrderStatus.delivered).toList();
      final todayCompletedOrders = todayOrders.where((order) => order.status == order_model.OrderStatus.delivered).toList();
      final weekCompletedOrders = weekOrders.where((order) => order.status == order_model.OrderStatus.delivered).toList();
      final monthCompletedOrders = monthOrders.where((order) => order.status == order_model.OrderStatus.delivered).toList();
      final lastMonthCompletedOrders = lastMonthOrders.where((order) => order.status == order_model.OrderStatus.delivered).toList();

      // Recalculate revenue for completed orders only
      final completedTotalRevenue = _calculateRevenue(completedOrders);
      final completedTodayRevenue = _calculateRevenue(todayCompletedOrders);
      final completedWeekRevenue = _calculateRevenue(weekCompletedOrders);
      final completedMonthRevenue = _calculateRevenue(monthCompletedOrders);
      final completedLastMonthRevenue = _calculateRevenue(lastMonthCompletedOrders);

      // Recalculate growth based on completed orders
      final completedRevenueGrowth = _calculateGrowthPercentage(completedMonthRevenue, completedLastMonthRevenue);
      final completedOrderGrowth = _calculateGrowthPercentage(
        monthCompletedOrders.length.toDouble(),
        lastMonthCompletedOrders.length.toDouble()
      );

      // Debug logging for completed orders
      if (kDebugMode) {
        print('Analytics Debug: Total orders (all): ${orders.length}');
        print('Analytics Debug: Total completed orders: ${completedOrders.length}');
        print('Analytics Debug: Today orders (all): ${todayOrders.length}');
        print('Analytics Debug: Today completed orders: ${todayCompletedOrders.length}');
        print('Analytics Debug: Total revenue (completed): ₹${completedTotalRevenue.toStringAsFixed(2)}');
        print('Analytics Debug: Today revenue (completed): ₹${completedTodayRevenue.toStringAsFixed(2)}');
      }

      return DashboardAnalytics(
        totalOrders: completedOrders.length, // Only completed orders
        todayOrders: todayCompletedOrders.length, // Only today's completed orders
        weekOrders: weekCompletedOrders.length,
        monthOrders: monthCompletedOrders.length,
        totalRevenue: completedTotalRevenue, // Only revenue from completed orders
        todayRevenue: completedTodayRevenue, // Only today's completed revenue
        weekRevenue: completedWeekRevenue,
        monthRevenue: completedMonthRevenue,
        revenueGrowth: completedRevenueGrowth,
        orderGrowth: completedOrderGrowth,
        pendingOrders: statusCounts[order_model.OrderStatus.pending] ?? 0,
        confirmedOrders: statusCounts[order_model.OrderStatus.confirmed] ?? 0,
        processingOrders: statusCounts[order_model.OrderStatus.processing] ?? 0,
        outForDeliveryOrders: statusCounts[order_model.OrderStatus.outForDelivery] ?? 0,
        deliveredOrders: statusCounts[order_model.OrderStatus.delivered] ?? 0,
        cancelledOrders: statusCounts[order_model.OrderStatus.cancelled] ?? 0,
        partialCancelledOrders: statusCounts[order_model.OrderStatus.partialCancelled] ?? 0,
        refundedOrders: statusCounts[order_model.OrderStatus.refunded] ?? 0,
        todayPendingOrders: todayStatusCounts[order_model.OrderStatus.pending] ?? 0,
        todayConfirmedOrders: todayStatusCounts[order_model.OrderStatus.confirmed] ?? 0,
        todayDeliveredOrders: todayStatusCounts[order_model.OrderStatus.delivered] ?? 0,
        topProducts: topProducts,
        averageOrderValue: completedOrders.isNotEmpty ? completedTotalRevenue / completedOrders.length : 0.0,
        conversionRate: _calculateConversionRate(orders),
      );
    } catch (e) {
      if (e is AnalyticsException) rethrow;
      throw AnalyticsException('Failed to get dashboard analytics: $e');
    }
  }

  /// Get revenue trends for charts
  Future<List<RevenueDataPoint>> getRevenueTrends(int days) async {
    try {
      if (!_authService.isAuthenticated) {
        throw AnalyticsException('Authentication required');
      }

      final now = DateTime.now();
      final startDate = now.subtract(Duration(days: days));

      // Try to query by timestamp first (for legacy orders), then fall back to createdAt
      QuerySnapshot ordersSnapshot;
      try {
        ordersSnapshot = await _firestore
            .collection('orders')
            .where('timestamp', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
            .orderBy('timestamp')
            .get();
      } catch (e) {
        // Fallback to createdAt if timestamp field doesn't exist
        ordersSnapshot = await _firestore
            .collection('orders')
            .where('createdAt', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
            .orderBy('createdAt')
            .get();
      }

      final orders = ordersSnapshot.docs
          .map((doc) {
            try {
              return order_model.Order.fromFirestore(doc);
            } catch (e) {
              if (kDebugMode) {
                print('Error parsing order ${doc.id}: $e');
              }
              return null;
            }
          })
          .where((order) => order != null)
          .cast<order_model.Order>()
          .toList();

      final Map<String, double> dailyRevenue = {};

      for (int i = 0; i < days; i++) {
        final date = startDate.add(Duration(days: i));
        final dateKey = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
        dailyRevenue[dateKey] = 0.0;
      }

      for (final order in orders) {
        // For delivered orders, use delivery date for accurate delivery analytics
        // For other orders, use creation date
        DateTime orderDate;
        if (order.status == order_model.OrderStatus.delivered && order.deliveryDate != null) {
          orderDate = order.deliveryDate!;
        } else {
          orderDate = order.createdAt;
        }

        final dateKey = '${orderDate.year}-${orderDate.month.toString().padLeft(2, '0')}-${orderDate.day.toString().padLeft(2, '0')}';
        if (dailyRevenue.containsKey(dateKey)) {
          dailyRevenue[dateKey] = (dailyRevenue[dateKey] ?? 0.0) + order.totalAmount;
        }
      }

      return dailyRevenue.entries
          .map((entry) => RevenueDataPoint(
                date: DateTime.parse(entry.key),
                revenue: entry.value,
              ))
          .toList();
    } catch (e) {
      if (e is AnalyticsException) rethrow;
      throw AnalyticsException('Failed to get revenue trends: $e');
    }
  }

  /// Get delivery-focused analytics (based on delivery dates for delivered orders)
  Future<DeliveryAnalytics> getDeliveryAnalytics() async {
    try {
      if (!_authService.isAuthenticated) {
        throw AnalyticsException('Authentication required');
      }

      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final weekStart = today.subtract(Duration(days: now.weekday - 1));
      final monthStart = DateTime(now.year, now.month, 1);

      // Get all delivered orders
      final deliveredOrdersSnapshot = await _firestore
          .collection('orders')
          .where('status', isEqualTo: order_model.OrderStatus.delivered.name)
          .get();

      final deliveredOrders = deliveredOrdersSnapshot.docs
          .map((doc) {
            try {
              return order_model.Order.fromFirestore(doc);
            } catch (e) {
              if (kDebugMode) {
                print('Error parsing delivered order ${doc.id}: $e');
              }
              return null;
            }
          })
          .where((order) => order != null)
          .cast<order_model.Order>()
          .toList();

      // Filter by delivery date
      final todayDeliveries = deliveredOrders.where((order) {
        final deliveryDate = order.deliveryDate ?? order.createdAt;
        return deliveryDate.isAfter(today.subtract(const Duration(seconds: 1))) &&
               deliveryDate.isBefore(today.add(const Duration(days: 1)));
      }).toList();

      final weekDeliveries = deliveredOrders.where((order) {
        final deliveryDate = order.deliveryDate ?? order.createdAt;
        return deliveryDate.isAfter(weekStart.subtract(const Duration(seconds: 1))) &&
               deliveryDate.isBefore(now.add(const Duration(seconds: 1)));
      }).toList();

      final monthDeliveries = deliveredOrders.where((order) {
        final deliveryDate = order.deliveryDate ?? order.createdAt;
        return deliveryDate.isAfter(monthStart.subtract(const Duration(seconds: 1))) &&
               deliveryDate.isBefore(now.add(const Duration(seconds: 1)));
      }).toList();

      // Calculate delivery metrics
      final todayRevenue = todayDeliveries.fold(0.0, (total, order) => total + order.totalAmount);
      final weekRevenue = weekDeliveries.fold(0.0, (total, order) => total + order.totalAmount);
      final monthRevenue = monthDeliveries.fold(0.0, (total, order) => total + order.totalAmount);

      return DeliveryAnalytics(
        totalDeliveries: deliveredOrders.length,
        todayDeliveries: todayDeliveries.length,
        weekDeliveries: weekDeliveries.length,
        monthDeliveries: monthDeliveries.length,
        todayRevenue: todayRevenue,
        weekRevenue: weekRevenue,
        monthRevenue: monthRevenue,
        averageDeliveryValue: deliveredOrders.isNotEmpty
            ? deliveredOrders.fold(0.0, (total, order) => total + order.totalAmount) / deliveredOrders.length
            : 0.0,
      );
    } catch (e) {
      if (e is AnalyticsException) rethrow;
      throw AnalyticsException('Failed to get delivery analytics: $e');
    }
  }

  /// Get order status distribution
  Future<Map<order_model.OrderStatus, int>> getOrderStatusDistribution() async {
    try {
      if (!_authService.isAuthenticated) {
        throw AnalyticsException('Authentication required');
      }

      final ordersSnapshot = await _firestore.collection('orders').get();
      final orders = ordersSnapshot.docs
          .map((doc) {
            try {
              return order_model.Order.fromFirestore(doc);
            } catch (e) {
              if (kDebugMode) {
                print('Error parsing order ${doc.id}: $e');
              }
              return null;
            }
          })
          .where((order) => order != null)
          .cast<order_model.Order>()
          .toList();

      return _calculateStatusCounts(orders);
    } catch (e) {
      if (e is AnalyticsException) rethrow;
      throw AnalyticsException('Failed to get order status distribution: $e');
    }
  }

  /// Helper methods
  List<order_model.Order> _filterOrdersByDate(
    List<order_model.Order> orders,
    DateTime start,
    DateTime end,
  ) {
    return orders.where((order) {
      // For delivered orders, use delivery date for accurate delivery analytics
      // For other orders, use creation date
      DateTime orderDate;
      if (order.status == order_model.OrderStatus.delivered && order.deliveryDate != null) {
        orderDate = order.deliveryDate!;
      } else {
        orderDate = order.createdAt;
      }

      return orderDate.isAfter(start.subtract(const Duration(seconds: 1))) &&
             orderDate.isBefore(end.add(const Duration(seconds: 1)));
    }).toList();
  }

  double _calculateRevenue(List<order_model.Order> orders) {
    // Only count revenue from delivered orders for accurate business metrics
    return orders
        .where((order) => order.status == order_model.OrderStatus.delivered)
        .fold(0.0, (total, order) => total + order.totalAmount);
  }

  Map<order_model.OrderStatus, int> _calculateStatusCounts(List<order_model.Order> orders) {
    final Map<order_model.OrderStatus, int> counts = {};
    for (final status in order_model.OrderStatus.values) {
      counts[status] = 0;
    }
    
    for (final order in orders) {
      counts[order.status] = (counts[order.status] ?? 0) + 1;
    }
    
    return counts;
  }

  double _calculateGrowthPercentage(double current, double previous) {
    if (previous == 0) return current > 0 ? 100.0 : 0.0;
    return ((current - previous) / previous) * 100;
  }

  double _calculateConversionRate(List<order_model.Order> orders) {
    if (orders.isEmpty) return 0.0;
    final deliveredOrders = orders.where((o) => o.status == order_model.OrderStatus.delivered).length;
    return (deliveredOrders / orders.length) * 100;
  }

  Future<List<TopProduct>> _getTopProducts(DateTime start, DateTime end) async {
    try {
      final ordersSnapshot = await _firestore
          .collection('orders')
          .where('createdAt', isGreaterThanOrEqualTo: Timestamp.fromDate(start))
          .where('createdAt', isLessThanOrEqualTo: Timestamp.fromDate(end))
          .get();

      final orders = ordersSnapshot.docs
          .map((doc) => order_model.Order.fromFirestore(doc))
          .where((order) => order.status != order_model.OrderStatus.cancelled)
          .toList();

      final Map<String, TopProduct> productStats = {};

      for (final order in orders) {
        for (final item in order.items) {
          if (productStats.containsKey(item.productId)) {
            final existing = productStats[item.productId]!;
            productStats[item.productId] = TopProduct(
              productId: existing.productId,
              productName: existing.productName,
              totalQuantity: existing.totalQuantity + item.quantity,
              totalRevenue: existing.totalRevenue + (item.price * item.quantity),
            );
          } else {
            productStats[item.productId] = TopProduct(
              productId: item.productId,
              productName: item.productName,
              totalQuantity: item.quantity,
              totalRevenue: item.price * item.quantity,
            );
          }
        }
      }

      final topProducts = productStats.values.toList();
      topProducts.sort((a, b) => b.totalRevenue.compareTo(a.totalRevenue));
      return topProducts.take(10).toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting top products: $e');
      }
      return [];
    }
  }
}

/// Analytics data models
class DashboardAnalytics {
  final int totalOrders;
  final int todayOrders;
  final int weekOrders;
  final int monthOrders;
  final double totalRevenue;
  final double todayRevenue;
  final double weekRevenue;
  final double monthRevenue;
  final double revenueGrowth;
  final double orderGrowth;
  final int pendingOrders;
  final int confirmedOrders;
  final int processingOrders;
  final int outForDeliveryOrders;
  final int deliveredOrders;
  final int cancelledOrders;
  final int partialCancelledOrders;
  final int refundedOrders;
  final int todayPendingOrders;
  final int todayConfirmedOrders;
  final int todayDeliveredOrders;
  final List<TopProduct> topProducts;
  final double averageOrderValue;
  final double conversionRate;

  DashboardAnalytics({
    required this.totalOrders,
    required this.todayOrders,
    required this.weekOrders,
    required this.monthOrders,
    required this.totalRevenue,
    required this.todayRevenue,
    required this.weekRevenue,
    required this.monthRevenue,
    required this.revenueGrowth,
    required this.orderGrowth,
    required this.pendingOrders,
    required this.confirmedOrders,
    required this.processingOrders,
    required this.outForDeliveryOrders,
    required this.deliveredOrders,
    required this.cancelledOrders,
    required this.partialCancelledOrders,
    required this.refundedOrders,
    required this.todayPendingOrders,
    required this.todayConfirmedOrders,
    required this.todayDeliveredOrders,
    required this.topProducts,
    required this.averageOrderValue,
    required this.conversionRate,
  });
}

class RevenueDataPoint {
  final DateTime date;
  final double revenue;

  RevenueDataPoint({required this.date, required this.revenue});
}

class TopProduct {
  final String productId;
  final String productName;
  final int totalQuantity;
  final double totalRevenue;

  TopProduct({
    required this.productId,
    required this.productName,
    required this.totalQuantity,
    required this.totalRevenue,
  });
}

class DeliveryAnalytics {
  final int totalDeliveries;
  final int todayDeliveries;
  final int weekDeliveries;
  final int monthDeliveries;
  final double todayRevenue;
  final double weekRevenue;
  final double monthRevenue;
  final double averageDeliveryValue;

  DeliveryAnalytics({
    required this.totalDeliveries,
    required this.todayDeliveries,
    required this.weekDeliveries,
    required this.monthDeliveries,
    required this.todayRevenue,
    required this.weekRevenue,
    required this.monthRevenue,
    required this.averageDeliveryValue,
  });
}

class AnalyticsException implements Exception {
  final String message;
  
  AnalyticsException(this.message);
  
  @override
  String toString() => message;
}
