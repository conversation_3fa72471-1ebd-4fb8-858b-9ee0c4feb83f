
class Product {
  final String id;
  final String name;
  final String description;
  final double price;
  final double mrp;
  final String imageUrl;
  final String category;
  int quantity;

  Product({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.mrp,
    required this.imageUrl,
    required this.category,
    this.quantity = 0,
  });

  factory Product.fromFirestore(Map<String, dynamic> data, String documentId) {
    return Product(
      id: documentId,
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      price: (data['price'] ?? 0).toDouble(),
      mrp: (data['mrp'] ?? 0).toDouble(),
      imageUrl: data['imageUrl'] ?? '',
      category: data['category'] ?? '',
    );
  }

  // Discount calculations
  double get discountAmount => mrp - price;
  double get discountPercentage => mrp > 0 ? ((mrp - price) / mrp) * 100 : 0;
  bool get hasDiscount => mrp > price;

  String get formattedDiscountPercentage => '${discountPercentage.toStringAsFixed(0)}% OFF';
  String get formattedPrice => '₹${price.toStringAsFixed(2)}';
  String get formattedMrp => '₹${mrp.toStringAsFixed(2)}';
}
