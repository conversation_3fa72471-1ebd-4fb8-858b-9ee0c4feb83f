// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;
import 'config/environment.dart';

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: Environment.firebaseApiKey,
    appId: Environment.firebaseAppId,
    messagingSenderId: Environment.firebaseMessagingSenderId,
    projectId: Environment.firebaseProjectId,
    authDomain: Environment.firebaseAuthDomain,
    storageBucket: Environment.firebaseStorageBucket,
    measurementId: Environment.firebaseMeasurementId,
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAGMRayUL9zAB95GB7Xb5uSmFP_4AokqjE',
    appId: '1:841436133900:android:2d89da5a9ffae3939e0c29',
    messagingSenderId: '841436133900',
    projectId: 'nativfresh-e5b0f',
    storageBucket: 'nativfresh-e5b0f.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAGMRayUL9zAB95GB7Xb5uSmFP_4AokqjE',
    appId: '1:841436133900:ios:YOUR_IOS_APP_ID',
    messagingSenderId: '841436133900',
    projectId: 'nativfresh-e5b0f',
    storageBucket: 'nativfresh-e5b0f.firebasestorage.app',
    iosBundleId: 'com.nativfresh.delivery',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyAGMRayUL9zAB95GB7Xb5uSmFP_4AokqjE',
    appId: '1:841436133900:macos:YOUR_MACOS_APP_ID',
    messagingSenderId: '841436133900',
    projectId: 'nativfresh-e5b0f',
    storageBucket: 'nativfresh-e5b0f.firebasestorage.app',
    iosBundleId: 'com.nativfresh.delivery',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAGMRayUL9zAB95GB7Xb5uSmFP_4AokqjE',
    appId: '1:841436133900:windows:YOUR_WINDOWS_APP_ID',
    messagingSenderId: '841436133900',
    projectId: 'nativfresh-e5b0f',
    storageBucket: 'nativfresh-e5b0f.firebasestorage.app',
  );
}
