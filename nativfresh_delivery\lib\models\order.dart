// Copy the order model from admin app for consistency
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

enum OrderStatus {
  pending,
  confirmed,
  processing,
  outForDelivery,
  delivered,
  cancelled,
  partialCancelled,
  refunded
}

enum PaymentMethod {
  cash,
  upi,
  card
}

class OrderItem {
  final String productId;
  final String productName;
  final int quantity;
  final double price;
  final String? imageUrl;

  OrderItem({
    required this.productId,
    required this.productName,
    required this.quantity,
    required this.price,
    this.imageUrl,
  });

  factory OrderItem.fromMap(Map<String, dynamic> data) {
    return OrderItem(
      productId: data['productId'] ?? '',
      productName: data['productName'] ?? '',
      quantity: data['quantity'] ?? 0,
      price: (data['price'] ?? 0).toDouble(),
      imageUrl: data['imageUrl'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'productId': productId,
      'productName': productName,
      'quantity': quantity,
      'price': price,
      'imageUrl': imageUrl,
    };
  }

  double get totalPrice => price * quantity;
}

class CustomerInfo {
  final String name;
  final String phoneNumber;
  final String? email;

  CustomerInfo({
    required this.name,
    required this.phoneNumber,
    this.email,
  });

  factory CustomerInfo.fromMap(Map<String, dynamic> data) {
    return CustomerInfo(
      name: data['name'] ?? '',
      phoneNumber: data['phoneNumber'] ?? '',
      email: data['email'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'phoneNumber': phoneNumber,
      'email': email,
    };
  }
}

class DeliveryAddress {
  final String apartmentName;
  final String blockName;
  final String houseNumber;

  DeliveryAddress({
    required this.apartmentName,
    required this.blockName,
    required this.houseNumber,
  });

  factory DeliveryAddress.fromMap(Map<String, dynamic> data) {
    return DeliveryAddress(
      apartmentName: data['apartmentName'] ?? '',
      blockName: data['blockName'] ?? '',
      houseNumber: data['houseNumber'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'apartmentName': apartmentName,
      'blockName': blockName,
      'houseNumber': houseNumber,
    };
  }

  String get fullAddress => '$houseNumber, $blockName, $apartmentName';
}

class Order {
  final String id;
  final String orderNumber;
  final List<OrderItem> items;
  final double subtotal;
  final double deliveryFee;
  final double totalAmount;
  final OrderStatus status;
  final CustomerInfo customerInfo;
  final DeliveryAddress deliveryAddress;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deliveryDate;
  final String? notes;
  final String? cancellationReason;
  final String? assignedDeliveryPersonId;
  final PaymentMethod? paymentMethod;
  final bool? paymentCollected;

  Order({
    required this.id,
    required this.orderNumber,
    required this.items,
    required this.subtotal,
    this.deliveryFee = 0.0,
    required this.totalAmount,
    this.status = OrderStatus.pending,
    required this.customerInfo,
    required this.deliveryAddress,
    required this.createdAt,
    required this.updatedAt,
    this.deliveryDate,
    this.notes,
    this.cancellationReason,
    this.assignedDeliveryPersonId,
    this.paymentMethod,
    this.paymentCollected,
  });

  factory Order.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    try {
      // Parse items
      final itemsData = data['items'] as List<dynamic>? ?? [];
      final items = itemsData.map((item) => OrderItem.fromMap(item as Map<String, dynamic>)).toList();

      // Calculate totals
      final subtotal = (data['subtotal'] ?? 0).toDouble();
      final deliveryFee = (data['deliveryFee'] ?? 0).toDouble();
      final totalAmount = (data['totalAmount'] ?? subtotal + deliveryFee).toDouble();

      // Parse timestamps
      final createdAt = _parseTimestamp(data['createdAt']);
      final updatedAt = _parseTimestamp(data['updatedAt']);

      // Generate order number from document ID
      String orderNumber = data['orderNumber'] ?? doc.id.substring(0, 8).toUpperCase();

      // Parse customer info with fallback for legacy format
      CustomerInfo customerInfo;
      if (data['customerInfo'] != null) {
        customerInfo = CustomerInfo.fromMap(data['customerInfo'] as Map<String, dynamic>);
      } else {
        // Legacy format fallback
        customerInfo = CustomerInfo(
          name: data['userName'] ?? data['customerName'] ?? 'Unknown Customer',
          phoneNumber: data['userPhone'] ?? data['customerPhone'] ?? '',
          email: data['userEmail'] ?? data['customerEmail'],
        );
      }

      // Parse delivery address with fallback for legacy format
      DeliveryAddress deliveryAddress;
      if (data['deliveryAddress'] != null) {
        deliveryAddress = DeliveryAddress.fromMap(data['deliveryAddress'] as Map<String, dynamic>);
      } else {
        // Legacy format fallback
        deliveryAddress = DeliveryAddress(
          apartmentName: data['apartmentName'] ?? '',
          blockName: data['blockName'] ?? '',
          houseNumber: data['houseNumber'] ?? '',
        );
      }

      return Order(
        id: doc.id,
        orderNumber: orderNumber,
        items: items,
        subtotal: subtotal,
        deliveryFee: deliveryFee,
        totalAmount: totalAmount,
        status: _parseOrderStatus(data['status']),
        customerInfo: customerInfo,
        deliveryAddress: deliveryAddress,
        createdAt: createdAt,
        updatedAt: updatedAt,
        deliveryDate: data['deliveryDate'] != null ? _parseTimestamp(data['deliveryDate']) : null,
        notes: data['notes'],
        cancellationReason: data['cancellationReason'],
        assignedDeliveryPersonId: data['assignedDeliveryPersonId'],
        paymentMethod: _parsePaymentMethod(data['paymentMethod']),
        paymentCollected: data['paymentCollected'],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error parsing order ${doc.id}: $e');
        print('Order data: $data');
      }
      rethrow;
    }
  }

  static OrderStatus _parseOrderStatus(dynamic status) {
    if (status is String) {
      try {
        return OrderStatus.values.firstWhere(
          (e) => e.name == status,
          orElse: () => OrderStatus.pending,
        );
      } catch (e) {
        return OrderStatus.pending;
      }
    }
    return OrderStatus.pending;
  }

  static PaymentMethod? _parsePaymentMethod(dynamic method) {
    if (method is String) {
      try {
        return PaymentMethod.values.firstWhere(
          (e) => e.name == method,
        );
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  static DateTime _parseTimestamp(dynamic timestamp) {
    if (timestamp is Timestamp) {
      return timestamp.toDate();
    }
    if (timestamp is DateTime) {
      return timestamp;
    }
    if (timestamp is String) {
      try {
        return DateTime.parse(timestamp);
      } catch (e) {
        return DateTime.now();
      }
    }
    return DateTime.now();
  }

  // Business logic methods
  bool get canUpdateStatus => 
      status == OrderStatus.outForDelivery;
  
  bool get isCompleted => 
      status == OrderStatus.delivered || 
      status == OrderStatus.cancelled || 
      status == OrderStatus.refunded;
  
  bool get isActive => !isCompleted;
  
  String get statusDisplayName {
    switch (status) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.confirmed:
        return 'Confirmed';
      case OrderStatus.processing:
        return 'Processing';
      case OrderStatus.outForDelivery:
        return 'Out for Delivery';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
      case OrderStatus.partialCancelled:
        return 'Partial Cancelled';
      case OrderStatus.refunded:
        return 'Refunded';
    }
  }

  String get formattedTotalAmount => '₹${totalAmount.toStringAsFixed(2)}';

  int get totalItems => items.fold(0, (total, item) => total + item.quantity);

  // Create a copy of the order with updated fields
  Order copyWith({
    String? id,
    String? orderNumber,
    List<OrderItem>? items,
    double? subtotal,
    double? deliveryFee,
    double? totalAmount,
    OrderStatus? status,
    CustomerInfo? customerInfo,
    DeliveryAddress? deliveryAddress,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deliveryDate,
    String? notes,
    String? cancellationReason,
    String? assignedDeliveryPersonId,
    PaymentMethod? paymentMethod,
    bool? paymentCollected,
  }) {
    return Order(
      id: id ?? this.id,
      orderNumber: orderNumber ?? this.orderNumber,
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      deliveryFee: deliveryFee ?? this.deliveryFee,
      totalAmount: totalAmount ?? this.totalAmount,
      status: status ?? this.status,
      customerInfo: customerInfo ?? this.customerInfo,
      deliveryAddress: deliveryAddress ?? this.deliveryAddress,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deliveryDate: deliveryDate ?? this.deliveryDate,
      notes: notes ?? this.notes,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      assignedDeliveryPersonId: assignedDeliveryPersonId ?? this.assignedDeliveryPersonId,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentCollected: paymentCollected ?? this.paymentCollected,
    );
  }
}
