import 'dart:math';

/// Utility class for generating unique 5-digit alphanumeric usernames
class UsernameGenerator {
  static const String _chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  static final Random _random = Random();

  /// Generates a 5-digit alphanumeric username
  /// Format: [A-Z0-9]{5}
  /// Example: A3B7K, X9M2P, etc.
  static String generate() {
    final buffer = StringBuffer();
    
    for (int i = 0; i < 5; i++) {
      buffer.write(_chars[_random.nextInt(_chars.length)]);
    }
    
    return buffer.toString();
  }

  /// Generates a unique username by checking against existing usernames
  /// Returns a username that doesn't exist in the provided list
  static String generateUnique(List<String> existingUsernames) {
    String username;
    int attempts = 0;
    const maxAttempts = 100; // Prevent infinite loop
    
    do {
      username = generate();
      attempts++;
      
      if (attempts >= maxAttempts) {
        // If we can't find a unique username after many attempts,
        // add a timestamp suffix to ensure uniqueness
        final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
        username = generate().substring(0, 3) + timestamp.substring(timestamp.length - 2);
        break;
      }
    } while (existingUsernames.contains(username));
    
    return username;
  }

  /// Validates if a username follows the correct format
  static bool isValidFormat(String username) {
    if (username.length != 5) return false;
    
    final regex = RegExp(r'^[A-Z0-9]{5}$');
    return regex.hasMatch(username);
  }
}
