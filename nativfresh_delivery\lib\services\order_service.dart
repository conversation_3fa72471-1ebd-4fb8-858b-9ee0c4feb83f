import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/order.dart' as order_model;
import 'auth_service.dart';
import 'notification_service.dart';

class OrderService {
  static final OrderService _instance = OrderService._internal();
  factory OrderService() => _instance;
  OrderService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AuthService _authService = AuthService();

  // Get orders assigned to current delivery person
  Stream<List<order_model.Order>> getAssignedOrders() {
    return Stream.fromFuture(_authService.isAuthenticated).asyncExpand((isAuth) {
      if (!isAuth) {
        return Stream.value([]);
      }

      // Get delivery person ID from local storage or Firebase Auth
      return Stream.fromFuture(_getDeliveryPersonId()).asyncExpand((deliveryPersonId) {
        if (deliveryPersonId == null) {
          return Stream.value([]);
        }

        return _firestore
            .collection('orders')
            .where('assignedDeliveryPersonId', isEqualTo: deliveryPersonId)
            .where('status', whereIn: [order_model.OrderStatus.outForDelivery.name])
            .orderBy('createdAt', descending: true)
            .snapshots()
            .map((snapshot) => snapshot.docs
                .map((doc) => order_model.Order.fromFirestore(doc))
                .toList());
      });
    });
  }

  // Get delivery person ID from Firebase Auth or local storage
  Future<String?> _getDeliveryPersonId() async {
    try {
      // Try to get ID from Firebase Auth first
      if (_authService.currentUser != null) {
        return _authService.currentUser!.uid;
      } else {
        // Get ID from local storage for admin-authenticated users
        final deliveryPersonInfo = await _authService.getSavedDeliveryPersonInfo();
        return deliveryPersonInfo?['id'];
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error getting delivery person ID: $e');
      }
      return null;
    }
  }

  // Get specific order details
  Future<order_model.Order?> getOrder(String orderId) async {
    try {
      if (!(await _authService.isAuthenticated)) {
        throw Exception('Authentication required');
      }

      final doc = await _firestore.collection('orders').doc(orderId).get();
      
      if (!doc.exists) {
        return null;
      }

      final order = order_model.Order.fromFirestore(doc);
      
      // Verify this order is assigned to current delivery person
      if (order.assignedDeliveryPersonId != _authService.currentUser!.uid) {
        throw Exception('You are not authorized to view this order');
      }

      return order;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting order: $e');
      }
      rethrow;
    }
  }

  // Update order status
  Future<void> updateOrderStatus(String orderId, order_model.OrderStatus newStatus) async {
    try {
      if (!(await _authService.isAuthenticated)) {
        throw Exception('Authentication required');
      }

      // Get the current order to verify assignment
      final order = await getOrder(orderId);
      if (order == null) {
        throw Exception('Order not found');
      }

      // Only allow delivery person to mark as delivered
      if (newStatus != order_model.OrderStatus.delivered) {
        throw Exception('You can only mark orders as delivered');
      }

      final updateData = <String, dynamic>{
        'status': newStatus.name,
        'updatedAt': FieldValue.serverTimestamp(),
      };

      if (newStatus == order_model.OrderStatus.delivered) {
        updateData['deliveryDate'] = FieldValue.serverTimestamp();
      }

      await _firestore
          .collection('orders')
          .doc(orderId)
          .update(updateData);

      if (kDebugMode) {
        print('Order $orderId status updated to ${newStatus.name}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating order status: $e');
      }
      rethrow;
    }
  }

  // Update payment collection status
  Future<void> updatePaymentStatus(String orderId, order_model.PaymentMethod paymentMethod, bool collected) async {
    try {
      if (!(await _authService.isAuthenticated)) {
        throw Exception('Authentication required');
      }

      // Verify order assignment
      final order = await getOrder(orderId);
      if (order == null) {
        throw Exception('Order not found');
      }

      await _firestore
          .collection('orders')
          .doc(orderId)
          .update({
            'paymentMethod': paymentMethod.name,
            'paymentCollected': collected,
            'updatedAt': FieldValue.serverTimestamp(),
          });

      if (kDebugMode) {
        print('Payment status updated for order $orderId: ${paymentMethod.name} - $collected');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating payment status: $e');
      }
      rethrow;
    }
  }

  // Get delivery statistics for current delivery person
  Future<Map<String, int>> getDeliveryStats() async {
    try {
      if (!(await _authService.isAuthenticated)) {
        return {'total': 0, 'today': 0, 'pending': 0};
      }

      final deliveryPersonId = _authService.currentUser!.uid;
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);

      // Get total deliveries
      final totalQuery = await _firestore
          .collection('orders')
          .where('assignedDeliveryPersonId', isEqualTo: deliveryPersonId)
          .where('status', isEqualTo: order_model.OrderStatus.delivered.name)
          .get();

      // Get today's deliveries
      final todayQuery = await _firestore
          .collection('orders')
          .where('assignedDeliveryPersonId', isEqualTo: deliveryPersonId)
          .where('status', isEqualTo: order_model.OrderStatus.delivered.name)
          .where('deliveryDate', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .get();

      // Get pending deliveries
      final pendingQuery = await _firestore
          .collection('orders')
          .where('assignedDeliveryPersonId', isEqualTo: deliveryPersonId)
          .where('status', isEqualTo: order_model.OrderStatus.outForDelivery.name)
          .get();

      return {
        'total': totalQuery.docs.length,
        'today': todayQuery.docs.length,
        'pending': pendingQuery.docs.length,
      };
    } catch (e) {
      if (kDebugMode) {
        print('Error getting delivery stats: $e');
      }
      return {'total': 0, 'today': 0, 'pending': 0};
    }
  }

  // Get all delivered orders for current delivery person
  Future<List<order_model.Order>> getDeliveredOrders() async {
    try {
      final deliveryPersonId = await _getDeliveryPersonId();
      if (deliveryPersonId == null) {
        return [];
      }

      final querySnapshot = await _firestore
          .collection('orders')
          .where('assignedDeliveryPersonId', isEqualTo: deliveryPersonId)
          .where('status', isEqualTo: order_model.OrderStatus.delivered.name)
          .orderBy('deliveredAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => order_model.Order.fromFirestore(doc))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting delivered orders: $e');
      }
      return [];
    }
  }

  // Get today's delivered orders for current delivery person
  Future<List<order_model.Order>> getTodayDeliveredOrders() async {
    try {
      final deliveryPersonId = await _getDeliveryPersonId();
      if (deliveryPersonId == null) {
        return [];
      }

      final now = DateTime.now();
      final startOfDay = DateTime(now.year, now.month, now.day);
      final endOfDay = DateTime(now.year, now.month, now.day, 23, 59, 59);

      final querySnapshot = await _firestore
          .collection('orders')
          .where('assignedDeliveryPersonId', isEqualTo: deliveryPersonId)
          .where('status', isEqualTo: order_model.OrderStatus.delivered.name)
          .where('deliveredAt', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .where('deliveredAt', isLessThanOrEqualTo: Timestamp.fromDate(endOfDay))
          .orderBy('deliveredAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => order_model.Order.fromFirestore(doc))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting today\'s delivered orders: $e');
      }
      return [];
    }
  }
}
