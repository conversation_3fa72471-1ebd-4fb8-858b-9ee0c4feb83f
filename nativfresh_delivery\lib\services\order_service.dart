import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/order.dart' as order_model;
import 'auth_service.dart';

class OrderService {
  static final OrderService _instance = OrderService._internal();
  factory OrderService() => _instance;
  OrderService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AuthService _authService = AuthService();

  // Get orders assigned to current delivery person
  Stream<List<order_model.Order>> getAssignedOrders() {
    return Stream.fromFuture(_authService.isAuthenticated).asyncExpand((isAuth) {
      if (!isAuth) {
        return Stream.value([]);
      }

      // Get delivery person ID from local storage or Firebase Auth
      return Stream.fromFuture(_getDeliveryPersonId()).asyncExpand((deliveryPersonId) {
        if (deliveryPersonId == null) {
          return Stream.value([]);
        }

        return _firestore
            .collection('orders')
            .where('assignedDeliveryPersonId', isEqualTo: deliveryPersonId)
            .where('status', whereIn: [
              order_model.OrderStatus.outForDelivery.name,
              order_model.OrderStatus.processing.name, // Include processing orders too
            ])
            .orderBy('createdAt', descending: true)
            .snapshots()
            .map((snapshot) {
              if (kDebugMode) {
                print('Found ${snapshot.docs.length} orders for delivery person $deliveryPersonId');
              }
              return snapshot.docs
                  .map((doc) {
                    try {
                      final order = order_model.Order.fromFirestore(doc);
                      if (kDebugMode) {
                        print('Order ${order.orderNumber}: ${order.customerInfo.name} - ${order.deliveryAddress.fullAddress}');
                      }
                      return order;
                    } catch (e) {
                      if (kDebugMode) {
                        print('Error parsing order ${doc.id}: $e');
                      }
                      rethrow;
                    }
                  })
                  .toList();
            });
      });
    });
  }

  // Get delivery person ID from Firebase Auth or local storage
  Future<String?> _getDeliveryPersonId() async {
    try {
      // Try to get ID from Firebase Auth first
      if (_authService.currentUser != null) {
        return _authService.currentUser!.uid;
      } else {
        // Get ID from local storage for admin-authenticated users
        final deliveryPersonInfo = await _authService.getSavedDeliveryPersonInfo();
        return deliveryPersonInfo?['id'];
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error getting delivery person ID: $e');
      }
      return null;
    }
  }

  // Get specific order details
  Future<order_model.Order?> getOrder(String orderId) async {
    try {
      if (!(await _authService.isAuthenticated)) {
        throw Exception('Authentication required');
      }

      final deliveryPersonId = await _getDeliveryPersonId();
      if (deliveryPersonId == null) {
        throw Exception('Delivery person ID not found');
      }

      final doc = await _firestore.collection('orders').doc(orderId).get();

      if (!doc.exists) {
        if (kDebugMode) {
          print('Order $orderId not found');
        }
        return null;
      }

      if (kDebugMode) {
        print('Fetching order $orderId data: ${doc.data()}');
      }

      final order = order_model.Order.fromFirestore(doc);

      // Verify this order is assigned to current delivery person
      if (order.assignedDeliveryPersonId != deliveryPersonId) {
        throw Exception('You are not authorized to view this order');
      }

      if (kDebugMode) {
        print('Order details: ${order.orderNumber}, Customer: ${order.customerInfo.name}, Address: ${order.deliveryAddress.fullAddress}');
      }

      return order;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting order $orderId: $e');
      }
      rethrow;
    }
  }

  // Update order status
  Future<void> updateOrderStatus(String orderId, order_model.OrderStatus newStatus) async {
    try {
      if (!(await _authService.isAuthenticated)) {
        throw Exception('Authentication required');
      }

      final deliveryPersonId = await _getDeliveryPersonId();
      if (deliveryPersonId == null) {
        throw Exception('Delivery person ID not found');
      }

      // Get the current order to verify assignment
      final orderDoc = await _firestore.collection('orders').doc(orderId).get();
      if (!orderDoc.exists) {
        throw Exception('Order not found');
      }

      final orderData = orderDoc.data() as Map<String, dynamic>;
      final assignedDeliveryPersonId = orderData['assignedDeliveryPersonId'];

      // Verify this order is assigned to current delivery person
      if (assignedDeliveryPersonId != deliveryPersonId) {
        throw Exception('You are not authorized to update this order');
      }

      // Allow delivery person to update status to delivered or other valid statuses
      final allowedStatuses = [
        order_model.OrderStatus.delivered,
        order_model.OrderStatus.outForDelivery, // Allow to update back to out for delivery if needed
      ];

      if (!allowedStatuses.contains(newStatus)) {
        throw Exception('You can only mark orders as delivered or out for delivery');
      }

      final updateData = <String, dynamic>{
        'status': newStatus.name,
        'updatedAt': FieldValue.serverTimestamp(),
      };

      if (newStatus == order_model.OrderStatus.delivered) {
        updateData['deliveredAt'] = FieldValue.serverTimestamp();
        updateData['deliveryDate'] = FieldValue.serverTimestamp();
      }

      await _firestore
          .collection('orders')
          .doc(orderId)
          .update(updateData);

      if (kDebugMode) {
        print('Order $orderId status updated to ${newStatus.name} by delivery person $deliveryPersonId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating order status: $e');
      }
      rethrow;
    }
  }

  // Update payment collection status
  Future<void> updatePaymentStatus(String orderId, order_model.PaymentMethod paymentMethod, bool collected) async {
    try {
      if (!(await _authService.isAuthenticated)) {
        throw Exception('Authentication required');
      }

      // Verify order assignment
      final order = await getOrder(orderId);
      if (order == null) {
        throw Exception('Order not found');
      }

      await _firestore
          .collection('orders')
          .doc(orderId)
          .update({
            'paymentMethod': paymentMethod.name,
            'paymentCollected': collected,
            'updatedAt': FieldValue.serverTimestamp(),
          });

      if (kDebugMode) {
        print('Payment status updated for order $orderId: ${paymentMethod.name} - $collected');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating payment status: $e');
      }
      rethrow;
    }
  }

  // Get delivery statistics for current delivery person
  Future<Map<String, int>> getDeliveryStats() async {
    try {
      if (!(await _authService.isAuthenticated)) {
        return {'total': 0, 'today': 0, 'pending': 0};
      }

      final deliveryPersonId = _authService.currentUser!.uid;
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);

      // Get total deliveries
      final totalQuery = await _firestore
          .collection('orders')
          .where('assignedDeliveryPersonId', isEqualTo: deliveryPersonId)
          .where('status', isEqualTo: order_model.OrderStatus.delivered.name)
          .get();

      // Get today's deliveries
      final todayQuery = await _firestore
          .collection('orders')
          .where('assignedDeliveryPersonId', isEqualTo: deliveryPersonId)
          .where('status', isEqualTo: order_model.OrderStatus.delivered.name)
          .where('deliveryDate', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .get();

      // Get pending deliveries
      final pendingQuery = await _firestore
          .collection('orders')
          .where('assignedDeliveryPersonId', isEqualTo: deliveryPersonId)
          .where('status', isEqualTo: order_model.OrderStatus.outForDelivery.name)
          .get();

      return {
        'total': totalQuery.docs.length,
        'today': todayQuery.docs.length,
        'pending': pendingQuery.docs.length,
      };
    } catch (e) {
      if (kDebugMode) {
        print('Error getting delivery stats: $e');
      }
      return {'total': 0, 'today': 0, 'pending': 0};
    }
  }

  // Get all delivered orders for current delivery person
  Future<List<order_model.Order>> getDeliveredOrders() async {
    try {
      final deliveryPersonId = await _getDeliveryPersonId();
      if (deliveryPersonId == null) {
        return [];
      }

      final querySnapshot = await _firestore
          .collection('orders')
          .where('assignedDeliveryPersonId', isEqualTo: deliveryPersonId)
          .where('status', isEqualTo: order_model.OrderStatus.delivered.name)
          .orderBy('deliveredAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => order_model.Order.fromFirestore(doc))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting delivered orders: $e');
      }
      return [];
    }
  }

  // Get today's delivered orders for current delivery person
  Future<List<order_model.Order>> getTodayDeliveredOrders() async {
    try {
      final deliveryPersonId = await _getDeliveryPersonId();
      if (deliveryPersonId == null) {
        return [];
      }

      final now = DateTime.now();
      final startOfDay = DateTime(now.year, now.month, now.day);
      final endOfDay = DateTime(now.year, now.month, now.day, 23, 59, 59);

      final querySnapshot = await _firestore
          .collection('orders')
          .where('assignedDeliveryPersonId', isEqualTo: deliveryPersonId)
          .where('status', isEqualTo: order_model.OrderStatus.delivered.name)
          .where('deliveredAt', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .where('deliveredAt', isLessThanOrEqualTo: Timestamp.fromDate(endOfDay))
          .orderBy('deliveredAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => order_model.Order.fromFirestore(doc))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting today\'s delivered orders: $e');
      }
      return [];
    }
  }

  // Get available status options for delivery person
  List<order_model.OrderStatus> getAvailableStatusOptions(order_model.OrderStatus currentStatus) {
    switch (currentStatus) {
      case order_model.OrderStatus.outForDelivery:
        return [
          order_model.OrderStatus.delivered,
          // Allow to mark back to out for delivery if there are issues
        ];
      case order_model.OrderStatus.delivered:
        return []; // Cannot change from delivered
      default:
        return [order_model.OrderStatus.outForDelivery]; // Can only set to out for delivery
    }
  }

  // Get status display name for delivery app
  String getStatusDisplayName(order_model.OrderStatus status) {
    switch (status) {
      case order_model.OrderStatus.pending:
        return 'Pending';
      case order_model.OrderStatus.confirmed:
        return 'Confirmed';
      case order_model.OrderStatus.processing:
        return 'Processing';
      case order_model.OrderStatus.outForDelivery:
        return 'Out for Delivery';
      case order_model.OrderStatus.delivered:
        return 'Delivered';
      case order_model.OrderStatus.cancelled:
        return 'Cancelled';
      case order_model.OrderStatus.partialCancelled:
        return 'Partial Cancelled';
      case order_model.OrderStatus.refunded:
        return 'Refunded';
    }
  }

  // Add delivery notes
  Future<void> addDeliveryNotes(String orderId, String notes) async {
    try {
      if (!(await _authService.isAuthenticated)) {
        throw Exception('Authentication required');
      }

      final deliveryPersonId = await _getDeliveryPersonId();
      if (deliveryPersonId == null) {
        throw Exception('Delivery person ID not found');
      }

      await _firestore
          .collection('orders')
          .doc(orderId)
          .update({
            'deliveryNotes': notes,
            'deliveryNotesAddedBy': deliveryPersonId,
            'deliveryNotesAddedAt': FieldValue.serverTimestamp(),
            'updatedAt': FieldValue.serverTimestamp(),
          });

      if (kDebugMode) {
        print('Delivery notes added to order $orderId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error adding delivery notes: $e');
      }
      rethrow;
    }
  }
}
