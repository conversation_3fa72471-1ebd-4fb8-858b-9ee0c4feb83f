import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:convert'; // For jsonEncode and jsonDecode

class RegistrationScreen extends StatefulWidget {
  const RegistrationScreen({super.key});

  @override
  State<RegistrationScreen> createState() => _RegistrationScreenState();
}

class _RegistrationScreenState extends State<RegistrationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _apartmentController = TextEditingController();

  // List to hold controllers for dynamic block/house number pairs
  final List<Map<String, TextEditingController>> _blockHouseNumberControllers = [];

  @override
  void initState() {
    super.initState();
    // Add an initial block/house number field
    _addBlockField();
  }

  @override
  void dispose() {
    _apartmentController.dispose();
    for (var entry in _blockHouseNumberControllers) {
      entry['block']?.dispose();
      entry['houseNumbers']?.dispose();
    }
    super.dispose();
  }

  void _addBlockField() {
    setState(() {
      _blockHouseNumberControllers.add({
        'block': TextEditingController(),
        'houseNumbers': TextEditingController(),
      });
    });
  }

  void _removeBlockField(int index) {
    setState(() {
      _blockHouseNumberControllers[index]['block']?.dispose();
      _blockHouseNumberControllers[index]['houseNumbers']?.dispose();
      _blockHouseNumberControllers.removeAt(index);
    });
  }

  Future<void> _register() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('apartment', _apartmentController.text.trim());

    // Prepare the list of blocks and house numbers to save as JSON
    List<Map<String, String>> registeredBlocks = [];
    for (var entry in _blockHouseNumberControllers) {
      registeredBlocks.add({
        'block': entry['block']!.text.trim(),
        'houseNumbers': entry['houseNumbers']!.text.trim(),
      });
    }
    await prefs.setString('registeredBlocks', jsonEncode(registeredBlocks));

    // Navigate to landing screen - AuthWrapper will handle the routing
    if (mounted) {
      Navigator.of(context).pushReplacementNamed('/landing');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Register Your Address'),
      ),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  FirebaseAuth.instance.currentUser?.displayName != null
                      ? 'Welcome ${FirebaseAuth.instance.currentUser!.displayName}!'
                      : 'Welcome to Nativfresh!',
                  style: const TextStyle(fontSize: 28, fontWeight: FontWeight.bold, color: Colors.green),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 10),
                const Text(
                  'Please tell us where you live to get started.',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 40),
                TextFormField(
                  controller: _apartmentController,
                  decoration: const InputDecoration(
                    labelText: 'Apartment Name',
                    hintText: 'e.g., Green Valley Apartments',
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your apartment name';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 20),
                // Dynamically added block and house number fields
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _blockHouseNumberControllers.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 20.0),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Column(
                              children: [
                                TextFormField(
                                  controller: _blockHouseNumberControllers[index]['block'],
                                  decoration: const InputDecoration(
                                    labelText: 'Block Name',
                                    hintText: 'e.g., Block A',
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Please enter block name';
                                    }
                                    return null;
                                  },
                                ),
                                const SizedBox(height: 15),
                                TextFormField(
                                  controller: _blockHouseNumberControllers[index]['houseNumbers'],
                                  decoration: const InputDecoration(
                                    labelText: 'House Number(s)',
                                    hintText: 'e.g., 101, 102, 103 (comma-separated)',
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Please enter house number(s)';
                                    }
                                    return null;
                                  },
                                ),
                              ],
                            ),
                          ),
                          if (_blockHouseNumberControllers.length > 1) // Only show remove if more than one block
                            IconButton(
                              icon: const Icon(Icons.remove_circle, color: Colors.red),
                              onPressed: () => _removeBlockField(index),
                            ),
                        ],
                      ),
                    );
                  },
                ),
                Align(
                  alignment: Alignment.centerRight,
                  child: TextButton.icon(
                    onPressed: _addBlockField,
                    icon: const Icon(Icons.add_circle, color: Colors.green),
                    label: const Text('Add Another Block'),
                  ),
                ),
                const SizedBox(height: 40),
                ElevatedButton(
                  onPressed: _register,
                  child: const Text('Register'),
                ),
                const SizedBox(height: 20),
                TextButton(
                  onPressed: () {
                    // Show dialog to confirm skipping registration
                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          title: const Text('Skip Registration'),
                          content: const Text(
                            'Are you sure you want to skip registration? You can register later from the app settings.',
                          ),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(),
                              child: const Text('Cancel'),
                            ),
                            TextButton(
                              onPressed: () async {
                                Navigator.of(context).pop();
                                // Set a minimal registration to proceed
                                final prefs = await SharedPreferences.getInstance();
                                await prefs.setString('apartment', 'Not Specified');
                                await prefs.setString('registeredBlocks', jsonEncode([
                                  {'block': 'Not Specified', 'houseNumbers': 'Not Specified'}
                                ]));
                                if (context.mounted) {
                                  Navigator.of(context).pushReplacementNamed('/landing');
                                }
                              },
                              child: const Text('Skip'),
                            ),
                          ],
                        );
                      },
                    );
                  },
                  child: const Text(
                    'Skip Registration (Register Later)',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}