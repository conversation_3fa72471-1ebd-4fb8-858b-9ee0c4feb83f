rules_version = '2';

// Firebase Storage Security Rules
service firebase.storage {
  match /b/{bucket}/o {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
             request.auth.token.role == 'admin';
    }
    
    function isValidImageFile() {
      return request.resource.contentType.matches('image/.*') &&
             request.resource.size < 10 * 1024 * 1024; // 10MB limit
    }
    
    function isValidUserImageFile() {
      return request.resource.contentType.matches('image/.*') &&
             request.resource.size < 5 * 1024 * 1024; // 5MB limit
    }
    
    // Product images - only admins can upload/modify
    match /products/{imageId} {
      allow read: if true; // Anyone can view product images
      allow write, delete: if isAdmin() && isValidImageFile();
    }
    
    // User profile images
    match /users/{userId}/profile/{imageId} {
      allow read: if true; // Anyone can view profile images
      allow write, delete: if (isAuthenticated() && request.auth.uid == userId) || isAdmin();
      allow write: if isValidUserImageFile();
    }
    
    // App assets (logos, banners, etc.)
    match /app_assets/{assetId} {
      allow read: if true; // Anyone can view app assets
      allow write, delete: if isAdmin() && isValidImageFile();
    }
    
    // Temporary uploads folder (for processing)
    match /temp/{userId}/{fileName} {
      allow read, write: if isAuthenticated() && request.auth.uid == userId;
      allow delete: if isAdmin() || (isAuthenticated() && request.auth.uid == userId);
      allow write: if isValidImageFile();
    }
    
    // Deny access to all other paths
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
