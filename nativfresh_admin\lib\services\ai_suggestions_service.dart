import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/order.dart' as order_model;
import '../models/product.dart';
import '../services/auth_service.dart';
import '../services/product_service.dart';

/// AI-powered suggestions service for intelligent stock management
class AISuggestionsService {
  static final AISuggestionsService _instance = AISuggestionsService._internal();
  factory AISuggestionsService() => _instance;
  AISuggestionsService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AuthService _authService = AuthService();
  final ProductService _productService = ProductService();

  /// Get comprehensive AI suggestions for stock management
  Future<AISuggestions> getStockSuggestions() async {
    try {
      if (!_authService.isAuthenticated) {
        throw AISuggestionsException('Authentication required');
      }

      final now = DateTime.now();
      final thirtyDaysAgo = now.subtract(const Duration(days: 30));
      final sevenDaysAgo = now.subtract(const Duration(days: 7));

      // Get recent orders for analysis
      final ordersSnapshot = await _firestore
          .collection('orders')
          .where('createdAt', isGreaterThanOrEqualTo: Timestamp.fromDate(thirtyDaysAgo))
          .orderBy('createdAt', descending: true)
          .get();

      final orders = ordersSnapshot.docs
          .map((doc) => order_model.Order.fromFirestore(doc))
          .where((order) => order.status != order_model.OrderStatus.cancelled)
          .toList();

      // Get all products
      final products = await _productService.getAllProducts();

      // Analyze data and generate suggestions
      final lowStockAlerts = await _generateLowStockAlerts(products);
      final restockSuggestions = await _generateRestockSuggestions(products, orders);
      final trendingProducts = _analyzeTrendingProducts(orders, sevenDaysAgo);
      final slowMovingProducts = _analyzeSlowMovingProducts(products, orders, thirtyDaysAgo);
      final demandForecast = _generateDemandForecast(orders, products);
      final seasonalInsights = _generateSeasonalInsights(orders);
      final priceOptimization = _generatePriceOptimizationSuggestions(products, orders);

      return AISuggestions(
        lowStockAlerts: lowStockAlerts,
        restockSuggestions: restockSuggestions,
        trendingProducts: trendingProducts,
        slowMovingProducts: slowMovingProducts,
        demandForecast: demandForecast,
        seasonalInsights: seasonalInsights,
        priceOptimization: priceOptimization,
        generatedAt: now,
      );
    } catch (e) {
      if (e is AISuggestionsException) rethrow;
      throw AISuggestionsException('Failed to generate AI suggestions: $e');
    }
  }

  /// Generate low stock alerts
  Future<List<LowStockAlert>> _generateLowStockAlerts(List<Product> products) async {
    final alerts = <LowStockAlert>[];
    
    for (final product in products) {
      if (product.stockQuantity <= 5 && product.stockQuantity > 0) {
        final urgency = product.stockQuantity <= 2 ? AlertUrgency.critical : AlertUrgency.warning;
        
        alerts.add(LowStockAlert(
          productId: product.id,
          productName: product.name,
          currentStock: product.stockQuantity,
          recommendedStock: _calculateRecommendedStock(product),
          urgency: urgency,
          message: _generateLowStockMessage(product, urgency),
        ));
      }
    }
    
    return alerts;
  }

  /// Generate restock suggestions based on sales velocity
  Future<List<RestockSuggestion>> _generateRestockSuggestions(
    List<Product> products, 
    List<order_model.Order> orders
  ) async {
    final suggestions = <RestockSuggestion>[];
    final productSales = _calculateProductSalesVelocity(orders);
    
    for (final product in products) {
      final salesData = productSales[product.id];
      if (salesData != null && salesData.averageDailySales > 0) {
        final daysOfStock = product.stockQuantity / salesData.averageDailySales;
        
        if (daysOfStock <= 7) { // Less than a week of stock
          final recommendedQuantity = (salesData.averageDailySales * 30).ceil(); // 30 days worth
          
          suggestions.add(RestockSuggestion(
            productId: product.id,
            productName: product.name,
            currentStock: product.stockQuantity,
            recommendedQuantity: recommendedQuantity,
            salesVelocity: salesData.averageDailySales,
            daysOfStockRemaining: daysOfStock.round(),
            priority: daysOfStock <= 3 ? RestockPriority.high : RestockPriority.medium,
            estimatedCost: recommendedQuantity * product.price * 0.6, // Assuming 60% cost
          ));
        }
      }
    }
    
    suggestions.sort((a, b) => a.daysOfStockRemaining.compareTo(b.daysOfStockRemaining));
    return suggestions;
  }

  /// Analyze trending products
  List<TrendingProduct> _analyzeTrendingProducts(
    List<order_model.Order> orders, 
    DateTime sevenDaysAgo
  ) {
    final recentOrders = orders.where((o) => o.createdAt.isAfter(sevenDaysAgo)).toList();
    final productSales = <String, int>{};
    
    for (final order in recentOrders) {
      for (final item in order.items) {
        productSales[item.productId] = (productSales[item.productId] ?? 0) + item.quantity;
      }
    }
    
    final trending = productSales.entries
        .where((entry) => entry.value >= 5) // At least 5 units sold in last 7 days
        .map((entry) => TrendingProduct(
              productId: entry.key,
              productName: _getProductName(orders, entry.key),
              salesInLastWeek: entry.value,
              trendScore: _calculateTrendScore(entry.value),
            ))
        .toList();
    
    trending.sort((a, b) => b.trendScore.compareTo(a.trendScore));
    return trending.take(10).toList();
  }

  /// Analyze slow-moving products
  List<SlowMovingProduct> _analyzeSlowMovingProducts(
    List<Product> products,
    List<order_model.Order> orders,
    DateTime thirtyDaysAgo
  ) {
    final productSales = <String, int>{};
    
    for (final order in orders) {
      for (final item in order.items) {
        productSales[item.productId] = (productSales[item.productId] ?? 0) + item.quantity;
      }
    }
    
    final slowMoving = <SlowMovingProduct>[];
    
    for (final product in products) {
      final salesCount = productSales[product.id] ?? 0;
      if (salesCount <= 2 && product.stockQuantity > 10) { // Low sales but high stock
        slowMoving.add(SlowMovingProduct(
          productId: product.id,
          productName: product.name,
          currentStock: product.stockQuantity,
          salesInLastMonth: salesCount,
          recommendedAction: _getSlowMovingAction(product, salesCount),
          potentialLoss: product.stockQuantity * product.price * 0.3, // 30% potential loss
        ));
      }
    }
    
    return slowMoving;
  }

  /// Generate demand forecast
  List<DemandForecast> _generateDemandForecast(
    List<order_model.Order> orders,
    List<Product> products
  ) {
    final forecasts = <DemandForecast>[];
    final productSales = _calculateProductSalesVelocity(orders);
    
    for (final product in products.take(20)) { // Top 20 products
      final salesData = productSales[product.id];
      if (salesData != null) {
        final nextWeekDemand = (salesData.averageDailySales * 7).ceil();
        final nextMonthDemand = (salesData.averageDailySales * 30).ceil();
        
        forecasts.add(DemandForecast(
          productId: product.id,
          productName: product.name,
          nextWeekDemand: nextWeekDemand,
          nextMonthDemand: nextMonthDemand,
          confidence: _calculateForecastConfidence(salesData.salesHistory),
        ));
      }
    }
    
    return forecasts;
  }

  /// Generate seasonal insights
  List<SeasonalInsight> _generateSeasonalInsights(List<order_model.Order> orders) {
    final insights = <SeasonalInsight>[];
    
    // Analyze by day of week
    final dayOfWeekSales = <int, double>{};
    for (int i = 1; i <= 7; i++) {
      dayOfWeekSales[i] = 0.0;
    }
    
    for (final order in orders) {
      dayOfWeekSales[order.createdAt.weekday] = 
          (dayOfWeekSales[order.createdAt.weekday] ?? 0.0) + order.totalAmount;
    }
    
    final bestDay = dayOfWeekSales.entries.reduce((a, b) => a.value > b.value ? a : b);
    final worstDay = dayOfWeekSales.entries.reduce((a, b) => a.value < b.value ? a : b);
    
    insights.add(SeasonalInsight(
      type: InsightType.weeklyPattern,
      title: 'Weekly Sales Pattern',
      description: 'Best sales day: ${_getDayName(bestDay.key)}, Worst: ${_getDayName(worstDay.key)}',
      impact: 'High',
      recommendation: 'Focus marketing efforts on ${_getDayName(bestDay.key)}s',
    ));
    
    return insights;
  }

  /// Generate price optimization suggestions
  List<PriceOptimization> _generatePriceOptimizationSuggestions(
    List<Product> products,
    List<order_model.Order> orders
  ) {
    final optimizations = <PriceOptimization>[];
    final productSales = _calculateProductSalesVelocity(orders);
    
    for (final product in products.take(10)) {
      final salesData = productSales[product.id];
      if (salesData != null) {
        final suggestion = _analyzePriceOptimization(product, salesData);
        if (suggestion != null) {
          optimizations.add(suggestion);
        }
      }
    }
    
    return optimizations;
  }

  /// Helper methods
  Map<String, ProductSalesData> _calculateProductSalesVelocity(List<order_model.Order> orders) {
    final Map<String, List<int>> productDailySales = {};
    final Map<String, String> productNames = {};
    
    for (final order in orders) {
      for (final item in order.items) {
        productNames[item.productId] = item.productName;
        if (!productDailySales.containsKey(item.productId)) {
          productDailySales[item.productId] = [];
        }
        productDailySales[item.productId]!.add(item.quantity);
      }
    }
    
    final Map<String, ProductSalesData> result = {};
    
    for (final entry in productDailySales.entries) {
      final totalSales = entry.value.fold(0, (total, qty) => total + qty);
      final averageDailySales = totalSales / 30.0; // Assuming 30-day period
      
      result[entry.key] = ProductSalesData(
        productId: entry.key,
        productName: productNames[entry.key] ?? '',
        totalSales: totalSales,
        averageDailySales: averageDailySales,
        salesHistory: entry.value,
      );
    }
    
    return result;
  }

  int _calculateRecommendedStock(Product product) {
    // Simple algorithm: minimum 20 units or 2 weeks worth of average sales
    return 20;
  }

  String _generateLowStockMessage(Product product, AlertUrgency urgency) {
    switch (urgency) {
      case AlertUrgency.critical:
        return 'CRITICAL: Only ${product.stockQuantity} units left! Immediate restock required.';
      case AlertUrgency.warning:
        return 'WARNING: Low stock (${product.stockQuantity} units). Consider restocking soon.';
    }
  }

  double _calculateTrendScore(int salesInWeek) {
    return salesInWeek * 10.0; // Simple scoring algorithm
  }

  String _getProductName(List<order_model.Order> orders, String productId) {
    for (final order in orders) {
      for (final item in order.items) {
        if (item.productId == productId) {
          return item.productName;
        }
      }
    }
    return 'Unknown Product';
  }

  String _getSlowMovingAction(Product product, int salesCount) {
    if (salesCount == 0) {
      return 'Consider promotional pricing or discontinuation';
    } else {
      return 'Reduce stock levels and implement marketing campaign';
    }
  }

  double _calculateForecastConfidence(List<int> salesHistory) {
    if (salesHistory.length < 7) return 0.5;
    
    final variance = _calculateVariance(salesHistory);
    final mean = salesHistory.fold(0, (total, val) => total + val) / salesHistory.length;
    
    if (mean == 0) return 0.3;
    
    final coefficientOfVariation = variance / mean;
    return (1.0 - coefficientOfVariation).clamp(0.3, 0.95);
  }

  double _calculateVariance(List<int> values) {
    if (values.isEmpty) return 0.0;
    
    final mean = values.fold(0, (total, val) => total + val) / values.length;
    final squaredDiffs = values.map((val) => (val - mean) * (val - mean));
    return squaredDiffs.fold(0.0, (total, val) => total + val) / values.length;
  }

  String _getDayName(int weekday) {
    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    return days[weekday - 1];
  }

  PriceOptimization? _analyzePriceOptimization(Product product, ProductSalesData salesData) {
    if (salesData.averageDailySales > 5) {
      // High demand - suggest price increase
      return PriceOptimization(
        productId: product.id,
        productName: product.name,
        currentPrice: product.price,
        suggestedPrice: product.price * 1.1,
        reason: 'High demand detected',
        expectedImpact: 'Increase revenue by 10%',
        confidence: 0.8,
      );
    } else if (salesData.averageDailySales < 1) {
      // Low demand - suggest price decrease
      return PriceOptimization(
        productId: product.id,
        productName: product.name,
        currentPrice: product.price,
        suggestedPrice: product.price * 0.9,
        reason: 'Low demand detected',
        expectedImpact: 'Increase sales volume by 15%',
        confidence: 0.7,
      );
    }
    return null;
  }
}

/// AI Suggestions data models
class AISuggestions {
  final List<LowStockAlert> lowStockAlerts;
  final List<RestockSuggestion> restockSuggestions;
  final List<TrendingProduct> trendingProducts;
  final List<SlowMovingProduct> slowMovingProducts;
  final List<DemandForecast> demandForecast;
  final List<SeasonalInsight> seasonalInsights;
  final List<PriceOptimization> priceOptimization;
  final DateTime generatedAt;

  AISuggestions({
    required this.lowStockAlerts,
    required this.restockSuggestions,
    required this.trendingProducts,
    required this.slowMovingProducts,
    required this.demandForecast,
    required this.seasonalInsights,
    required this.priceOptimization,
    required this.generatedAt,
  });
}

enum AlertUrgency { warning, critical }
enum RestockPriority { low, medium, high }
enum InsightType { weeklyPattern, monthlyTrend, seasonalPattern }

class LowStockAlert {
  final String productId;
  final String productName;
  final int currentStock;
  final int recommendedStock;
  final AlertUrgency urgency;
  final String message;

  LowStockAlert({
    required this.productId,
    required this.productName,
    required this.currentStock,
    required this.recommendedStock,
    required this.urgency,
    required this.message,
  });
}

class RestockSuggestion {
  final String productId;
  final String productName;
  final int currentStock;
  final int recommendedQuantity;
  final double salesVelocity;
  final int daysOfStockRemaining;
  final RestockPriority priority;
  final double estimatedCost;

  RestockSuggestion({
    required this.productId,
    required this.productName,
    required this.currentStock,
    required this.recommendedQuantity,
    required this.salesVelocity,
    required this.daysOfStockRemaining,
    required this.priority,
    required this.estimatedCost,
  });
}

class TrendingProduct {
  final String productId;
  final String productName;
  final int salesInLastWeek;
  final double trendScore;

  TrendingProduct({
    required this.productId,
    required this.productName,
    required this.salesInLastWeek,
    required this.trendScore,
  });
}

class SlowMovingProduct {
  final String productId;
  final String productName;
  final int currentStock;
  final int salesInLastMonth;
  final String recommendedAction;
  final double potentialLoss;

  SlowMovingProduct({
    required this.productId,
    required this.productName,
    required this.currentStock,
    required this.salesInLastMonth,
    required this.recommendedAction,
    required this.potentialLoss,
  });
}

class DemandForecast {
  final String productId;
  final String productName;
  final int nextWeekDemand;
  final int nextMonthDemand;
  final double confidence;

  DemandForecast({
    required this.productId,
    required this.productName,
    required this.nextWeekDemand,
    required this.nextMonthDemand,
    required this.confidence,
  });
}

class SeasonalInsight {
  final InsightType type;
  final String title;
  final String description;
  final String impact;
  final String recommendation;

  SeasonalInsight({
    required this.type,
    required this.title,
    required this.description,
    required this.impact,
    required this.recommendation,
  });
}

class PriceOptimization {
  final String productId;
  final String productName;
  final double currentPrice;
  final double suggestedPrice;
  final String reason;
  final String expectedImpact;
  final double confidence;

  PriceOptimization({
    required this.productId,
    required this.productName,
    required this.currentPrice,
    required this.suggestedPrice,
    required this.reason,
    required this.expectedImpact,
    required this.confidence,
  });
}

class ProductSalesData {
  final String productId;
  final String productName;
  final int totalSales;
  final double averageDailySales;
  final List<int> salesHistory;

  ProductSalesData({
    required this.productId,
    required this.productName,
    required this.totalSales,
    required this.averageDailySales,
    required this.salesHistory,
  });
}

class AISuggestionsException implements Exception {
  final String message;

  AISuggestionsException(this.message);

  @override
  String toString() => message;
}
